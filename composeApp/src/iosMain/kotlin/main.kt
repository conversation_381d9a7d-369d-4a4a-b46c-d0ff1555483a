import androidx.compose.ui.window.ComposeUIViewController
import com.forthosewho.app.App
import com.forthosewho.app.analytics.di.SwiftLibDependencyFactory
import com.forthosewho.app.analytics.di.swiftLibDependenciesModule
import io.github.aakira.napier.DebugAntilog
import io.github.aakira.napier.Napier
import org.koin.core.KoinApplication
import platform.UIKit.UIViewController

fun MainViewController(): UIViewController {
    // Initialize Napier early
    Napier.base(DebugAntilog())

    return ComposeUIViewController(
        configure = {}
    ) {
        App()
    }
}

fun KoinApplication.provideSwiftLibDependencyFactory(factory: SwiftLibDependencyFactory) =
    modules(swiftLibDependenciesModule(factory))