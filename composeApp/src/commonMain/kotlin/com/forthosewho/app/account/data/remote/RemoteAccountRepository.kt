package com.forthosewho.app.account.data.remote

import com.forthosewho.app.account.data.entity.AccountResponseRaw
import com.forthosewho.app.account.domain.AccountRepository
import com.forthosewho.app.account.domain.model.Account
import com.forthosewho.app.account.domain.model.Account.Companion.emptyAccount
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.network.createUrl
import com.forthosewho.app.network.getResults
import com.github.michaelbull.result.Result
import io.ktor.client.HttpClient
import io.ktor.http.HttpMethod

//Account API endpoint: /v0/users/12345-6789-0000
private const val ACCOUNT_API = "/v0/users/{USER_ID}"

class RemoteAccountRepository(private val client: HttpClient, private val baseUrl: String) : AccountRepository {

    override suspend fun getAccount(
        userId: String,
        token: String
    ): Result<Account, ErrorResponse> = client.getResults<AccountResponseRaw, Account>(
        requestBlock = {
            createUrl(host = baseUrl, path = ACCOUNT_API.replace("{USER_ID}", userId)) {
                method = HttpMethod.Get
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { meRaw -> meRaw?.toDomain() ?: emptyAccount },
    )
}
