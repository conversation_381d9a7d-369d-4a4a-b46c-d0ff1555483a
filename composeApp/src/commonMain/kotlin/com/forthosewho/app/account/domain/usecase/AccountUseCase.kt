package com.forthosewho.app.account.domain.usecase

import com.forthosewho.app.account.data.remote.RemoteAccountRepository
import com.forthosewho.app.account.domain.model.Account
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase.Params
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.github.michaelbull.result.Err
import kotlinx.serialization.json.Json
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess

class AccountUseCase(
    private val remoteAccountRepository: RemoteAccountRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val setStoredValueUseCase: SetStoredValueUseCase = SetStoredValueUseCase(),
) {
    suspend operator fun invoke(): Result<Account, ErrorResponse> {
        return remoteAccountRepository.getAccount(userId = getUserId(), token = getToken()).onSuccess {
            setStoredValueUseCase.apply {
                invoke(Params(dataStoreValue = DataStoreValues.CLUES, value = Json.encodeToString(it.clues)))
            }
        }.onFailure { failure ->
            if (failure.code == 401) {
                return Err(ErrorResponse(failure.code, failure.message, failure.details, failure.exception))
            }
        }
    }

    private suspend fun getUserId(): String = getStoredValueUseCase.getString(key = DataStoreValues.USER_ID)

    private suspend fun getToken(): String = getStoredValueUseCase.getString(key = DataStoreValues.ACCESS_TOKEN)
}
