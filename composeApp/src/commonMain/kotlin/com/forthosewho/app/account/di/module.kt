package com.forthosewho.app.account.di

import com.forthosewho.app.account.data.remote.RemoteAccountRepository
import com.forthosewho.app.account.domain.usecase.AccountUseCase
import com.forthosewho.app.network.di.CURRENT_BASE_URL
import com.forthosewho.app.network.di.DEFAULT_CLIENT
import org.koin.core.qualifier.named
import org.koin.dsl.module

val accountModule = module {
    single { AccountUseCase(remoteAccountRepository = get()) }
    single { RemoteAccountRepository(client = get(named(DEFAULT_CLIENT)), baseUrl = get(named(CURRENT_BASE_URL))) }
}
