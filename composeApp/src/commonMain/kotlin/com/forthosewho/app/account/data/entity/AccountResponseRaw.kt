package com.forthosewho.app.account.data.entity

import com.forthosewho.app.account.domain.model.Account
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.platform.utils.empty
import kotlinx.serialization.Serializable

@Serializable
data class AccountResponseRaw(
    private val createdAt: String? = null,
    private val updatedAt: String? = null,
    private val userName: String? = null,
    private val clues: List<ClueRaw>? = null,
) {
    fun toDomain() = Account(
        createdAt = createdAt ?: String.empty(),
        updatedAt = updatedAt ?: String.empty(),
        userName = userName ?: String.empty(),
        clues = clues?.map { it.toDomain() } ?: emptyList()
    )
}

@Serializable
data class ClueRaw(
    val id: String? = null,
    val clue: String? = null,
) {
    companion object {
        fun from(clue: Clue) = ClueRaw(
            id = clue.id,
            clue = clue.clue
        )
    }

    fun toDomain() = Clue(
        id = id ?: String.empty(),
        clue = clue ?: String.empty()
    )
}