package com.forthosewho.app.account.domain.model

import com.forthosewho.app.platform.utils.empty

data class Account(
    private val createdAt: String,
    private val updatedAt: String,
    private val userName: String,
    internal val clues: List<Clue>,
) {

    companion object {

        val emptyAccount = Account(
            createdAt = String.empty(),
            updatedAt = String.empty(),
            userName = String.empty(),
            clues = listOf(),
        )
    }
}
