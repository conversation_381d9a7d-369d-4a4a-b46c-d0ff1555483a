package com.forthosewho.app.network.di

import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.network.domain.provideBasicClient
import com.forthosewho.app.network.domain.provideBearerClient
import com.forthosewho.app.network.domain.provideMeClient
import com.forthosewho.app.platform.utils.EnvironmentSelection
import com.forthosewho.app.platform.utils.PlatformSelection
import com.forthosewho.app.platform.utils.PlatformType
import kotlinx.coroutines.runBlocking
import org.koin.core.qualifier.named
import org.koin.dsl.module

const val AUTH_CLIENT = "authClient"
const val ME_CLIENT = "meClient"
const val DEFAULT_CLIENT = "defaultClient"
const val CURRENT_BASE_URL = "currentBaseUrl"
const val CURRENT_AUTH_URL = "currentAuthUrl"
const val CURRENT_CLIENT_ID = "currentClientId"
const val CURRENT_CLIENT_SECRET = "currentClientSecret"

private const val AUTH_URL = "auth.forthosewho.com"
private const val BASE_URL = "production.forthosewho.com"
private const val STAGING_AUTH_URL = "auth-staging.forthosewho.com"
private const val STAGING_BASE_URL = "staging.forthosewho.com"

private const val ANDROID_CLIENT_ID = "05ef4232-41aa-4fb1-aa88-ac56024dc92e"
private const val ANDROID_CLIENT_SECRET = "FGrg$^@ssAndroid"
private const val IOS_CLIENT_ID = "e4694a39-317e-481d-8637-7b580ed5d89d"
private const val IOS_CLIENT_SECRET = "FGrg$^@ssiOS"

val networkingModule = module {
    single(named(ME_CLIENT)) {
        provideMeClient(isDebug = EnvironmentSelection.isDebug)
    }
    single(named(CURRENT_BASE_URL)) {
        runBlocking {
            if (GetStoredValueUseCase().getBoolean(DataStoreValues.IS_STAGING)) {
                STAGING_BASE_URL
            } else {
                BASE_URL
            }
        }
    }
    single(named(CURRENT_AUTH_URL)) {
        runBlocking {
            if (GetStoredValueUseCase().getBoolean(DataStoreValues.IS_STAGING)) {
                STAGING_AUTH_URL
            } else {
                AUTH_URL
            }
        }
    }
    single(named(CURRENT_CLIENT_ID)) {
        when (PlatformSelection.type) {
            PlatformType.ANDROID -> ANDROID_CLIENT_ID
            PlatformType.IOS -> IOS_CLIENT_ID
        }
    }
    single(named(CURRENT_CLIENT_SECRET)) {
        when (PlatformSelection.type) {
            PlatformType.ANDROID -> ANDROID_CLIENT_SECRET
            PlatformType.IOS -> IOS_CLIENT_SECRET
        }
    }
    single(named(AUTH_CLIENT)) {
        provideBasicClient(
            clientId = get(named(CURRENT_CLIENT_ID)),
            clientSecret = get(named(CURRENT_CLIENT_SECRET)),
            isDebug = EnvironmentSelection.isDebug,
            authUrl = get(named(CURRENT_AUTH_URL))
        )
    }
    single(named(DEFAULT_CLIENT)) {
        provideBearerClient(
            basicClient = get(named(AUTH_CLIENT)),
            isDebug = EnvironmentSelection.isDebug,
            baseUrl = get(named(CURRENT_BASE_URL)),
            authUrl = get(named(CURRENT_AUTH_URL))
        )
    }
}
