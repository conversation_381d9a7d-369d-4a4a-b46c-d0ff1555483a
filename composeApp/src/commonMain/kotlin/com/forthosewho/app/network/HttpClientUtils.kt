package com.forthosewho.app.network

import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.network.data.entity.ResponseRaw
import com.forthosewho.app.network.domain.HTTPS
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import io.github.aakira.napier.Napier
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.forms.submitForm
import io.ktor.client.request.request
import com.github.michaelbull.result.Result
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.URLBuilder
import io.ktor.http.set

internal suspend inline fun <reified T : Any, R> HttpClient.submitFormRequest(
    url: String,
    formParameters: Parameters,
    crossinline onSuccess: (T?) -> R,
    crossinline onError: (ErrorResponse) -> ErrorResponse,
    logError: (String) -> Unit = { Napier.d { it } }
): Result<R, ErrorResponse> {
    return try {
        val response = this.submitForm(
            url = url,
            formParameters = formParameters,
            encodeInQuery = false
        )
        processResponse(response, onSuccess, onError, logError)
    } catch (e: Exception) {
        logError("Exception during form submission to $url: ${e.message}")
        Err(onError(ErrorResponse(exception = e)))
    }
}

internal suspend inline fun <reified T : Any, R> HttpClient.getResults(
    requestBlock: HttpRequestBuilder.() -> Unit,
    crossinline onSuccess: (T?) -> R,
    crossinline onError: (ErrorResponse) -> ErrorResponse,
    logError: (String) -> Unit = { Napier.d { it } }
): Result<R, ErrorResponse> {
    return try {
        val response = request(requestBlock)
        processResponse(response, onSuccess, onError, logError)
    } catch (e: Exception) {
        logError("Exception during request: ${e.message}")
        Err(onError(ErrorResponse(exception = e)))
    }
}

private suspend inline fun <reified T : Any, R> processResponse(
    response: HttpResponse,
    crossinline onSuccess: (T?) -> R,
    crossinline onError: (ErrorResponse) -> ErrorResponse,
    logError: (String) -> Unit
): Result<R, ErrorResponse> {
    return try {
        Napier.d { "HTTP Response: ${response.status}" }

        when (response.status) {
            HttpStatusCode.NoContent -> {
                Napier.d { "No content in response." }
                Ok(onSuccess(null))
            }

            HttpStatusCode.OK,
            HttpStatusCode.Created,
            HttpStatusCode.Accepted,
            HttpStatusCode.PartialContent,
            HttpStatusCode.MultiStatus -> {
                val body = response.parseBody<T>()
                Ok(onSuccess(body.data))
            }

            else -> {
                val errorResponse = response.parseError<T>(logError)
                Err(onError(errorResponse))
            }
        }
    } catch (e: Exception) {
        logError("Exception during response processing: ${e.message}")
        Err(onError(ErrorResponse(exception = e)))
    }
}

private suspend inline fun <reified T : Any> HttpResponse.parseBody(): ResponseRaw<T> {
    return try {
        val body = this.body<ResponseRaw<T>>()
        Napier.d { "Parsed response body: $body" }
        body
    } catch (e: Exception) {
        Napier.e { "Failed to parse response body: ${e.message}" }
        throw e
    }
}

private suspend inline fun <reified T : Any> HttpResponse.parseError(logError: (String) -> Unit): ErrorResponse {
    return try {
        val body = this.body<ResponseRaw<T>>()
        Napier.d { "Parsed error response body: $body" }
        body.error?.toError() ?: ErrorResponse(message = "Unknown error occurred.")
    } catch (e: Exception) {
        logError("Failed to parse error response: ${e.message}")
        ErrorResponse(exception = e)
    }
}

fun HttpRequestBuilder.createUrl(
    scheme: String? = HTTPS,
    host: String?,
    port: Int? = null,
    path: String? = null,
    block: URLBuilder.() -> Unit = {}
) {
    url.set(scheme, host, port, path) {
        block()
    }
}
