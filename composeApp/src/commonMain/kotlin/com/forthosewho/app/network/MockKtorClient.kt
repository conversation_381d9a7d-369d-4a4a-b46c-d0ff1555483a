package com.forthosewho.app.network

import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json

// Function to create a mock Ktor client
fun createMockedKtorClient(mockResponse: String, status: HttpStatusCode = HttpStatusCode.OK): HttpClient {
    val mockEngine = MockEngine { request ->
        respond(
            content = mockResponse,
            status = status,
            headers = headersOf("Content-Type" to listOf(ContentType.Application.Json.toString()))
        )
    }

    return HttpClient(mockEngine) {
        install(ContentNegotiation) {
            json(Json { ignoreUnknownKeys = true })
        }
    }
}
