package com.forthosewho.app.network.domain

import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import com.forthosewho.app.login.data.entity.TokenRaw
import com.forthosewho.app.login.data.remote.REFRESH_TOKEN
import com.forthosewho.app.login.data.remote.TOKEN_API
import com.forthosewho.app.network.data.entity.ResponseRaw
import com.forthosewho.app.platform.utils.empty
import io.github.aakira.napier.Napier
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.DefaultRequest
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BasicAuthCredentials
import io.ktor.client.plugins.auth.providers.BearerTokens
import io.ktor.client.plugins.auth.providers.basic
import io.ktor.client.plugins.auth.providers.bearer
import io.ktor.client.plugins.compression.ContentEncoding
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.request.forms.submitForm
import io.ktor.client.request.header
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.serialization.kotlinx.KotlinxSerializationConverter
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import sp.bvantur.inspektify.ktor.InspektifyKtor

// Constants
private const val CONTENT_TYPE = "Content-Type"
private const val APPLICATION_JSON = "application/json"
private const val MAX_RETRIES = 5
private const val RETRY_BASE_DELAY_MS = 2000L
private const val RETRY_MAX_DELAY_MS = 10000L
private const val RETRY_RANDOMIZATION_MS = 150L
private const val RETRY_BASE_MULTIPLIER = 2.0

const val HTTPS = "https"
const val TAG = "FTW"
const val TIMEOUT = 30_000L

// Shared JSON configuration
private val sharedJsonConfig = Json {
    prettyPrint = true
    isLenient = true
    explicitNulls = false
    ignoreUnknownKeys = true
}

fun provideBasicClient(clientId: String, clientSecret: String, isDebug: Boolean, authUrl: String) = HttpClient {
    if (isDebug) {
        install(InspektifyKtor)
    }
    install(Auth) {
        basic {
            sendWithoutRequest {
                it.url.host == authUrl
            }
            credentials {
                BasicAuthCredentials(
                    username = clientId,
                    password = clientSecret,
                )
            }
        }
    }
    install(Logging) {
        logger = object : Logger {
            override fun log(message: String) {
                Napier.v(tag = TAG, message = message)
            }
        }
        level = LogLevel.ALL
    }
    install(HttpRequestRetry) {
        retryOnServerErrors(maxRetries = 5)
        exponentialDelay(
            base = 2.0,
            baseDelayMs = 2000,
            maxDelayMs = 10000,
            randomizationMs = 150,
        )
    }
    install(ContentNegotiation) {
        register(ContentType.Text.Html, KotlinxSerializationConverter(Json {
            prettyPrint = true
            isLenient = true
            explicitNulls = false
            ignoreUnknownKeys = true

        }))
        json(Json {
            prettyPrint = true
            isLenient = true
            explicitNulls = false
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        connectTimeoutMillis = TIMEOUT
        requestTimeoutMillis = TIMEOUT
        socketTimeoutMillis = TIMEOUT
    }
    install(DefaultRequest) {
        header(CONTENT_TYPE, APPLICATION_JSON)
    }
}

fun provideBearerClient(basicClient: HttpClient, isDebug: Boolean, baseUrl: String, authUrl: String) = HttpClient {
    if (isDebug) {
        install(InspektifyKtor)
    }
    install(ContentEncoding) {
        gzip()
    }
    install(Auth) {
        bearer {
            sendWithoutRequest {
                it.url.host == baseUrl
            }
            loadTokens {
                BearerTokens(
                    accessToken = GetStoredValueUseCase().getString(DataStoreValues.ACCESS_TOKEN),
                    refreshToken = GetStoredValueUseCase().getString(DataStoreValues.REFRESH_TOKEN)
                )
            }
            refreshTokens {
                val refreshToken = basicClient.submitForm(
                    url = "$HTTPS://$authUrl$TOKEN_API",
                    formParameters = Parameters.build {
                        append(REFRESH_TOKEN, GetStoredValueUseCase().getString(DataStoreValues.REFRESH_TOKEN))
                    },
                    encodeInQuery = false // Send as form data
                ) { markAsRefreshTokenRequest() }.body<ResponseRaw<TokenRaw>>().data?.toToken()
                if (refreshToken == null) {
                    val setStoredValueUseCase = SetStoredValueUseCase()
                    setStoredValueUseCase.invoke(
                        SetStoredValueUseCase.Params(DataStoreValues.ACCESS_TOKEN, String.empty())
                    )
                    setStoredValueUseCase.invoke(
                        SetStoredValueUseCase.Params(DataStoreValues.REFRESH_TOKEN, String.empty())
                    )
                    setStoredValueUseCase.invoke(
                        SetStoredValueUseCase.Params(DataStoreValues.LOGGED_IN, false)
                    )
                }
                BearerTokens(
                    accessToken = refreshToken?.accessToken ?: String.empty(),
                    refreshToken = refreshToken?.refreshToken ?: String.empty(),
                )
            }
        }
    }
    install(Logging) {
        logger = object : Logger {
            override fun log(message: String) {
                Napier.v(tag = TAG, message = message)
            }
        }
        level = LogLevel.ALL
    }
    install(HttpRequestRetry) {
        retryOnServerErrors(maxRetries = 5)
        exponentialDelay(
            base = 2.0,
            baseDelayMs = 2000,
            maxDelayMs = 10000,
            randomizationMs = 150,
        )
    }
    install(ContentNegotiation) {
        register(ContentType.Text.Html, KotlinxSerializationConverter(Json {
            prettyPrint = true
            isLenient = true
            explicitNulls = false
            ignoreUnknownKeys = true

        }))
        json(Json {
            prettyPrint = true
            isLenient = true
            explicitNulls = false
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        connectTimeoutMillis = TIMEOUT
        requestTimeoutMillis = TIMEOUT
        socketTimeoutMillis = TIMEOUT
    }
    install(DefaultRequest) {
        header(CONTENT_TYPE, APPLICATION_JSON)
    }
}

fun provideMeClient(isDebug: Boolean): HttpClient = HttpClient {
    if (isDebug) {
        install(InspektifyKtor)
    }
    install(Logging) {
        logger = object : Logger {
            override fun log(message: String) {
                Napier.v(tag = TAG, message = message)
            }
        }
        level = LogLevel.ALL
    }
    install(HttpRequestRetry) {
        retryOnServerErrors(maxRetries = 5)
        exponentialDelay(
            base = 2.0,
            baseDelayMs = 2000,
            maxDelayMs = 10000,
            randomizationMs = 150,
        )
    }
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            explicitNulls = false
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        connectTimeoutMillis = TIMEOUT
        requestTimeoutMillis = TIMEOUT
        socketTimeoutMillis = TIMEOUT
    }
    install(DefaultRequest) {
        header(CONTENT_TYPE, APPLICATION_JSON)
    }
}
