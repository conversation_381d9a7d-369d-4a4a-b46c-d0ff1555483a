package com.forthosewho.app.network.data.entity

import com.forthosewho.app.login.domain.model.Details
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.platform.utils.empty
import kotlinx.serialization.Serializable

@Serializable
data class ResponseRaw<T>(
    private val success: Boolean = false,
    internal val error: ErrorRaw? = null,
    internal val data: T? = null,
) {
    fun isSuccess(): Boolean = success

    fun toError(): ErrorResponse = error?.toError() ?: ErrorResponse(
        code = 0,
        message = String.empty(),
        details = Details()
    )
}

@Serializable
data class ErrorRaw(
    internal val code: Int? = null,
    internal val message: String? = null,
    internal val details: DetailsRaw? = null,
) {
    fun toError() = ErrorResponse(
        code = code ?: 0,
        message = message ?: String.empty(),
        details = details?.toDetails() ?: Details()
    )
}

@Serializable
data class DetailsRaw(
    private val errors: List<String> = emptyList(),
) {
    fun toDetails() = Details(
        errors = errors
    )
}