package com.forthosewho.app.stories_home.ui.pulltorefresh

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember

class PullToRefreshLayoutState() {
    var refreshIndicatorState = mutableStateOf(RefreshIndicatorState.Default)
        private set


    fun updateRefreshState(refreshState: RefreshIndicatorState) {
        refreshIndicatorState.value = refreshState
    }

    fun refresh() {
        updateRefreshState(RefreshIndicatorState.Refreshing)
    }
}

@Composable
fun rememberPullToRefreshState(): PullToRefreshLayoutState = remember { PullToRefreshLayoutState() }