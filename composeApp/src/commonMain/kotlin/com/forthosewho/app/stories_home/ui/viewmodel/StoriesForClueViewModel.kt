package com.forthosewho.app.stories_home.ui.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.cachedIn
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.domain.usecase.GetStoriesUseCase
import com.forthosewho.app.stories_home.ui.CLUE_ID
import com.forthosewho.app.stories_home.ui.model.StoriesForClueListState
import com.forthosewho.app.stories_home.ui.paging.StoriesPagingSource
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock.System.now
import kotlinx.serialization.json.Json

class StoriesForClueViewModel(
    private val getStoriesUseCase: GetStoriesUseCase,
    private val savedStateHandle: SavedStateHandle,
    private val config: PagingConfig = PagingConfig(pageSize = PAGE_SIZE_LIMIT),
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase()
) : ViewModel() {

    private val clueId = savedStateHandle.get<String>(CLUE_ID) ?: String.empty()
    private val _lastRefreshTime: MutableStateFlow<Long> = MutableStateFlow(now().epochSeconds)
    private val _state = MutableStateFlow(StoriesForClueListState.State())
    val state = _state.asStateFlow()

    init {
        getUserCluesAndStories()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _pagingDataFlow = _lastRefreshTime.flatMapLatest { _ ->
        Pager(config = config) {
            StoriesPagingSource(
                useCase = getStoriesUseCase,
                clueIds = listOf(clueId)
            )
        }.flow.cachedIn(viewModelScope)
    }

    private fun getUserCluesAndStories() {
        viewModelScope.launch {
            val clues: String = getStoredValueUseCase.getString(key = DataStoreValues.CLUES, defaultVal = String.empty())
            _state.update { state ->
                state.copy(
                    userClues = if (clues.isNotEmpty()) {
                        Json.decodeFromString<List<Clue>>(clues).map { ClueView.from(it) }
                    } else {
                        listOf()
                    }
                )
            }
            _state.update { state ->
                state.copy(
                    screenState = StoriesForClueListState.ScreenState.StateInitial(
                        subtitle = state.userClues.find { it.id == clueId }?.clue ?: String.empty()
                    ),
                    stories = _pagingDataFlow.catch { onError(error = it) }.cachedIn(scope = viewModelScope)
                )
            }
        }
    }

    fun refresh() {
        _lastRefreshTime.value = now().epochSeconds
    }

    fun onError(error: Throwable) {
        _state.update { state -> state.copy(error = error) }
    }

    fun selectStory(story: Story) {
        _state.update { state -> state.copy(currentStory = story) }
    }

    fun onBackPressed() {
        _state.update { StoriesForClueListState.State(screenState = StoriesForClueListState.ScreenState.StateNavigateBack) }
    }
}
