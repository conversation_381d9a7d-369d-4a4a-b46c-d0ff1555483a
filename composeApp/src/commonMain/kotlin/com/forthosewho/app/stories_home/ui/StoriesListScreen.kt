package com.forthosewho.app.stories_home.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.assistant.ui.AssistantBottomSheetDialog
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.platform.utils.LazyPagingItems
import com.forthosewho.app.platform.utils.collectAsLazyPagingItems
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.ui.model.StoriesListState
import com.forthosewho.app.stories_home.ui.pulltorefresh.FtwPullToRefresh
import com.forthosewho.app.stories_home.ui.pulltorefresh.RefreshIndicatorState
import com.forthosewho.app.stories_home.ui.pulltorefresh.rememberPullToRefreshState
import com.forthosewho.app.stories_home.ui.viewmodel.StoriesViewModel
import com.forthosewho.app.theme.ScrollToTopHandler
import com.forthosewho.app.theme.bottomsheets.rememberSheetController
import com.forthosewho.app.theme.cards.BuildingFeedCard
import com.forthosewho.app.theme.cards.StoryCard
import com.forthosewho.app.theme.toolbars.StoriesTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.select_the_clue_you_want_to_explore
import forthosewho_app.composeapp.generated.resources.stories_for_all_your_clues
import forthosewho_app.composeapp.generated.resources.this_story_was_picked_because
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun StoryListScreen(
    storiesViewModel: StoriesViewModel = koinViewModel<StoriesViewModel>().apply { initializeViewModel() },
    listState: LazyListState = rememberLazyListState(),
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    scrollBehavior: TopAppBarScrollBehavior = TopAppBarDefaults.pinnedScrollBehavior(),
    onStoriesForCluesScreenNavigated: (String) -> Unit,
    onLogout: () -> Unit,
    onBackPressed: () -> Unit,
) {
    ScrollToTopHandler(listState)

    val state by storiesViewModel.state.collectAsState()

    val sourcesSheetController = rememberSheetController()
    val cluesForUserSheetController = rememberSheetController()
    val cluesForStorySheetController = rememberSheetController()
    val assistantSheetController = rememberSheetController()

    Scaffold(
        modifier = Modifier.fillMaxSize().nestedScroll(connection = scrollBehavior.nestedScrollConnection),
        topBar = {
            StoriesTopAppBar(
                title = stringResource(Res.string.stories_for_all_your_clues),
                actionIcon = Icons.Rounded.KeyboardArrowDown,
                actionIconContentDescription = "Action icon",
                modifier = Modifier.fillMaxWidth(),
                onActionClick = { cluesForUserSheetController.show() }
            )
        },
        snackbarHost = {
            SnackbarHost(
                modifier = Modifier.testTag(tag = ErrorBarDefaults.ErrorTestTag).navigationBarsPadding(),
                hostState = snackbarHostState,
            )
        },
        contentWindowInsets = WindowInsets.ime,
    ) { paddingValues ->

        val pullToRefreshState = rememberPullToRefreshState()

        LaunchedEffect(key1 = state.screenState) {
            when (state.screenState) {
                is StoriesListState.ScreenState.Initial -> {}

                is StoriesListState.ScreenState.NavigateToStoriesForClues -> {
                    onStoriesForCluesScreenNavigated((state.screenState as StoriesListState.ScreenState.NavigateToStoriesForClues).clueView.id)
                }

                is StoriesListState.ScreenState.NavigateBack -> {
                    onBackPressed()
                }

                is StoriesListState.ScreenState.StoriesLoaded -> {
                    pullToRefreshState.updateRefreshState(RefreshIndicatorState.Default)
                }

                is StoriesListState.ScreenState.StoriesLoading -> {
                    pullToRefreshState.updateRefreshState(RefreshIndicatorState.Refreshing)
                }

                is StoriesListState.ScreenState.NavigateToLogin -> {
                    onLogout()
                }
            }
        }

        FtwPullToRefresh(
            modifier = Modifier.fillMaxSize().padding(paddingValues = paddingValues),
            onRefresh = { storiesViewModel.refresh() },
            pullRefreshLayoutState = pullToRefreshState,
        ) {
            StoriesListScreenContent(
                modifier = Modifier.fillMaxSize().padding(paddingValues = paddingValues),
                viewModel = storiesViewModel,
                listState = listState,
                onStoryLabelClicked = { story ->
                    storiesViewModel.selectStory(story)
                    sourcesSheetController.show()
                },
                onClueLabelClicked = { story ->
                    storiesViewModel.selectStory(story)
                    cluesForStorySheetController.show()
                }
            )
        }
        state.currentStory?.let { story ->
            SourcesBottomSheetDialog(
                modifier = Modifier,
                sources = story.sourcesList,
                modalSheetState = sourcesSheetController.state,
                onDismissRequest = {
                    sourcesSheetController.hide()
                },
            )
        }
        state.currentStory?.let { story ->
            CluesBottomSheetDialog(
                modifier = Modifier,
                title = stringResource(Res.string.this_story_was_picked_because),
                cluesList = story.relevantClues.map { ClueView.from(clue = it) },
                modalSheetState = cluesForStorySheetController.state,
                onEditButtonClicked = {
                    assistantSheetController.show()
                    cluesForStorySheetController.hide()
                },
                onDismissRequest = { cluesForStorySheetController.hide() },
            )
        }
        CluesBottomSheetDialog(
            title = stringResource(Res.string.select_the_clue_you_want_to_explore),
            modifier = Modifier.padding(top = paddingValues.calculateTopPadding()),
            onDismissRequest = { cluesForUserSheetController.hide() },
            modalSheetState = cluesForUserSheetController.state,
            cluesList = state.userClues,
            onClueItemClicked = {
                storiesViewModel.onClueItemClicked(it)
                cluesForUserSheetController.hide()
            }
        )
        AssistantBottomSheetDialog(
            category = InitializeThreadCategory.CHANGE_CLUES,
            modalSheetState = assistantSheetController.state,
            onDismissRequest = { assistantSheetController.hide() },
        )
    }
}

@Composable
private fun StoriesListScreenContent(
    modifier: Modifier,
    viewModel: StoriesViewModel,
    listState: LazyListState,
    onStoryLabelClicked: (Story) -> Unit,
    onClueLabelClicked: (Story) -> Unit,
    content: @Composable () -> Unit = {},
) {
    content()
    val storiesCollected = viewModel.state.value.stories.collectAsLazyPagingItems()

    if (storiesCollected.itemCount == 0) {
        BuildingFeedCard(
            modifier = Modifier.fillMaxSize().padding(16.dp),
            refreshFeedAction = { viewModel.refresh(updateUI = false) }
        )
    } else {
        StoriesPaginatedList(
            state = listState,
            stories = storiesCollected,
            onStoryLabelClicked = { story -> onStoryLabelClicked(story) },
            onClueLabelClicked = { story -> onClueLabelClicked(story) },
            onError = viewModel::onError,
        )
    }
}

@Composable
internal fun StoriesPaginatedList(
    stories: LazyPagingItems<Story>,
    onError: (error: Throwable) -> Unit,
    onStoryLabelClicked: (Story) -> Unit,
    onClueLabelClicked: (Story) -> Unit,
    state: LazyListState = rememberLazyListState(),
    contentPadding: PaddingValues = PaddingValues(all = 16.dp),
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = 16.dp),
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
) {
    val itemModifier = Modifier.fillMaxWidth().wrapContentHeight()

    LazyColumn(
        modifier = Modifier,
        state = state,
        contentPadding = contentPadding,
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
    ) {
        items(count = stories.itemCount) { index ->
            stories[index]?.run {
                StoryCard(
                    story = this,
                    modifier = itemModifier,
                    onStoryLabelClicked = onStoryLabelClicked,
                    onClueLabelClicked = onClueLabelClicked,
                )
            }
        }
    }

    LaunchedEffect(stories.loadState) {
        when {
            stories.loadState.refresh is LoadState.Error -> onError((stories.loadState.refresh as LoadState.Error).error)
            stories.loadState.append is LoadState.Error -> onError((stories.loadState.append as LoadState.Error).error)
        }
    }
}
