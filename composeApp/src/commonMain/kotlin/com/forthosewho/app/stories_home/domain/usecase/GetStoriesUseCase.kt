package com.forthosewho.app.stories_home.domain.usecase

import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.platform.data.PaginatedData
import com.forthosewho.app.stories_home.domain.StoriesRepository
import com.forthosewho.app.stories_home.domain.model.Story
import com.github.michaelbull.result.Result

class GetStoriesUseCase(
    private val repository: StoriesRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
) {
    suspend fun invoke(param: Params): Result<PaginatedData<Story>, ErrorResponse> {
        return repository.getStories(
            userId = getUserId(),
            limit = param.limit,
            offset = param.offset,
            token = getToken(),
            clueIds = param.clueIds,
        )
    }

    private suspend fun getUserId(): String = getStoredValueUseCase.getString(key = DataStoreValues.USER_ID)

    private suspend fun getToken(): String = getStoredValueUseCase.getString(key = DataStoreValues.ACCESS_TOKEN)

    data class Params(
        val limit: Int,
        val offset: Int,
        val clueIds: List<String> = listOf()
    )
}
