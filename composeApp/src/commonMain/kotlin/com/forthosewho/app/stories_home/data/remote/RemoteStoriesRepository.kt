package com.forthosewho.app.stories_home.data.remote

import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.network.createUrl
import com.github.michaelbull.result.Result
import com.forthosewho.app.network.getResults
import com.forthosewho.app.platform.data.PaginatedData
import com.forthosewho.app.stories_home.data.entitiy.StoriesResponseRaw
import com.forthosewho.app.stories_home.data.entitiy.StoriesResponseRaw.Companion.emptyPaginatedData
import com.forthosewho.app.stories_home.domain.StoriesRepository
import com.forthosewho.app.stories_home.domain.model.Story
import io.ktor.client.HttpClient
import io.ktor.client.request.parameter
import io.ktor.http.HttpMethod

//Stories API endpoint: /v0/users/12345-6789-0000/stories
private const val STORIES_API = "/v0/users/{USER_ID}/stories"
private const val LIMIT = "limit"
private const val OFFSET = "offset"
private const val CLUE_IDS = "clueIds"

class RemoteStoriesRepository(private val client: HttpClient, private val baseUrl: String) : StoriesRepository {

    override suspend fun getStories(
        userId: String,
        limit: Int,
        offset: Int,
        token: String,
        clueIds: List<String>,
    ): Result<PaginatedData<Story>, ErrorResponse> = client.getResults<StoriesResponseRaw, PaginatedData<Story>>(
        requestBlock = {
            createUrl(host = baseUrl, path = STORIES_API.replace("{USER_ID}", userId)) {
                parameter(key = LIMIT, value = limit)
                parameter(key = OFFSET, value = offset)
                parameter(key = CLUE_IDS, value = clueIds.joinToString(","))
                method = HttpMethod.Get
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { meRaw -> meRaw?.toDomain() ?: emptyPaginatedData() },
    )
}
