package com.forthosewho.app.stories_home.ui

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.displayCutoutPadding
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.composables.core.ModalBottomSheet
import com.composables.core.ModalBottomSheetState
import com.composables.core.ModalSheetProperties
import com.composables.core.Scrim
import com.composables.core.Sheet
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.theme.buttons.MainClickToActionButton
import com.forthosewho.app.theme.cards.ClueComposable
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Cyan
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.toolbars.SmallStoriesTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.edit_profile
import org.jetbrains.compose.resources.stringResource

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CluesBottomSheetDialog(
    title: String,
    modifier: Modifier,
    modalSheetState: ModalBottomSheetState,
    cluesList: List<ClueView>,
    onDismissRequest: () -> Unit,
    onEditButtonClicked: (() -> Unit)? = null,
    onClueItemClicked: ((ClueView) -> Unit?)? = null,
) {
    ModalBottomSheet(
        state = modalSheetState,
        onDismiss = onDismissRequest,
        properties = ModalSheetProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        ),
    ) {
        Scrim(scrimColor = Color.Black.copy(0.3f), enter = fadeIn(), exit = fadeOut())
        Sheet(
            modifier = modifier
                .padding(top = 12.dp)
                .displayCutoutPadding()
                .statusBarsPadding()
                .padding(WindowInsets.navigationBars.only(WindowInsetsSides.Horizontal).asPaddingValues())
                .shadow(4.dp, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .background(White)
                .widthIn(max = 640.dp)
                .fillMaxWidth()
                .imePadding(),
        ) {
            ContentNew(
                modifier = Modifier.padding(
                    start = 20.dp,
                    end = 20.dp,
                    top = 10.dp,
                    bottom = 10.dp
                ),
                toolbarContent = {
                    SmallStoriesTopAppBar(
                        title = title,
                        titleStyle = MaterialTheme.typography.titleMedium,
                        actionIcon = Icons.Rounded.Close,
                        actionIconContentDescription = "Action icon",
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        onActionClick = { onDismissRequest() }
                    )
                },
                clueContent = {
                    CluesBottomList(
                        modifier = Modifier.wrapContentSize(),
                        cluesList = cluesList,
                        onClueClicked = { onClueItemClicked?.let { it1 -> it1(it) } },
                    )
                },
                ctaContent = {
                    onEditButtonClicked?.let {
                        Spacer(modifier = Modifier.height(16.dp))
                        MainClickToActionButton(
                            buttonText = stringResource(Res.string.edit_profile),
                            modifier = Modifier.wrapContentWidth().wrapContentHeight(),
                            colors = ButtonColors(
                                containerColor = Cyan,
                                contentColor = Blue,
                                disabledContainerColor = Cyan,
                                disabledContentColor = Blue,
                            ),
                            buttonTextColor = Blue,
                            onButtonClick = { onEditButtonClicked() }
                        )
                    }
                }
            )
        }
    }
}

@Composable
private fun ContentNew(
    modifier: Modifier = Modifier,
    toolbarContent: @Composable () -> Unit = {},
    clueContent: @Composable () -> Unit = {},
    ctaContent: @Composable () -> Unit = {},
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(8.dp))
        toolbarContent()
        Spacer(modifier = Modifier.height(10.dp))
        clueContent()
        ctaContent()
        Spacer(modifier = Modifier.height(38.dp))
    }
}

@Composable
fun CluesBottomList(
    modifier: Modifier = Modifier,
    cluesList: List<ClueView>,
    onClueClicked: (ClueView) -> Unit,
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = 10.dp),
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
) {
    Box(modifier = modifier.wrapContentSize()) {
        val scrollableState = rememberLazyListState()
        LazyColumn(
            verticalArrangement = verticalArrangement,
            horizontalAlignment = horizontalAlignment,
            state = scrollableState,
        ) {
            cluesList.forEach { clue ->
                item(key = clue.id) {
                    ClueComposable(
                        modifier = modifier.clickable { onClueClicked(clue) },
                        text = clue.clue,
                    )
                }
            }
        }
    }
}
