package com.forthosewho.app.stories_home.di

import com.forthosewho.app.network.di.CURRENT_BASE_URL
import com.forthosewho.app.network.di.DEFAULT_CLIENT
import com.forthosewho.app.stories_home.data.remote.RemoteStoriesRepository
import com.forthosewho.app.stories_home.domain.StoriesRepository
import com.forthosewho.app.stories_home.domain.usecase.GetStoriesUseCase
import com.forthosewho.app.stories_home.ui.viewmodel.StoriesForClueViewModel
import com.forthosewho.app.stories_home.ui.viewmodel.StoriesViewModel
import org.koin.core.module.dsl.viewModel
import org.koin.core.qualifier.named
import org.koin.dsl.module

val storiesModule = module {
    single { GetStoriesUseCase(repository = get()) }
    single<StoriesRepository> { RemoteStoriesRepository(client = get(named(DEFAULT_CLIENT)), baseUrl = get(named(CURRENT_BASE_URL))) }

    viewModel { StoriesViewModel(getStoriesUseCase = get(), accountUseCase = get(), analyticsUseCase = get()) }
    viewModel { StoriesForClueViewModel(getStoriesUseCase = get(), savedStateHandle = get()) }
}