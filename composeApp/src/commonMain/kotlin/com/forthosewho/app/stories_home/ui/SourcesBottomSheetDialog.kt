package com.forthosewho.app.stories_home.ui

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.displayCutoutPadding
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import com.composables.core.ModalBottomSheet
import com.composables.core.ModalBottomSheetState
import com.composables.core.ModalSheetProperties
import com.composables.core.Scrim
import com.composables.core.Sheet
import com.final_class.webview_multiplatform_mobile.webview.WebViewPlatform
import com.final_class.webview_multiplatform_mobile.webview.controller.rememberWebViewController
import com.final_class.webview_multiplatform_mobile.webview.settings.android.AndroidWebViewModifier
import com.final_class.webview_multiplatform_mobile.webview.settings.android.android_scheme_colors.ColorSchemeParams
import com.final_class.webview_multiplatform_mobile.webview.settings.android.defaultColorSchemeParams
import com.final_class.webview_multiplatform_mobile.webview.settings.android.showTitle
import com.final_class.webview_multiplatform_mobile.webview.settings.android.urlBarHidingEnabled
import com.forthosewho.app.stories_home.domain.model.Article
import com.forthosewho.app.theme.cards.SourceItem
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.toolbars.SmallStoriesTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.choose_sources_to_read_more
import org.jetbrains.compose.resources.stringResource

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SourcesBottomSheetDialog(
    modifier: Modifier,
    modalSheetState: ModalBottomSheetState,
    sources: List<Article>,
    onDismissRequest: () -> Unit,
) {
    val webViewController by rememberWebViewController()

    WebViewPlatform(
        webViewController = webViewController,
        androidSettings = AndroidWebViewModifier.showTitle(true).urlBarHidingEnabled(true).defaultColorSchemeParams(
            colorSchemeParams = ColorSchemeParams(
                toolbarColor = MaterialTheme.colorScheme.surface,
                navigationBarColor = MaterialTheme.colorScheme.surface,
                navigationBarDividerColor = MaterialTheme.colorScheme.surface,
                secondaryToolbarColor = MaterialTheme.colorScheme.secondary
            )
        )
    )
    ModalBottomSheet(
        state = modalSheetState,
        onDismiss = onDismissRequest,
        properties = ModalSheetProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        ),
    ) {
        Scrim(scrimColor = Black.copy(0.3f), enter = fadeIn(), exit = fadeOut())
        Sheet(
            modifier = modifier
                .padding(top = 12.dp)
                .displayCutoutPadding()
                .statusBarsPadding()
                .padding(WindowInsets.navigationBars.only(WindowInsetsSides.Horizontal).asPaddingValues())
                .shadow(4.dp, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .background(White)
                .widthIn(max = 640.dp)
                .fillMaxWidth()
                .imePadding(),
        ) {
            Column(modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 20.dp, bottom = 34.dp)) {
                SmallStoriesTopAppBar(
                    title = stringResource(Res.string.choose_sources_to_read_more, sources.size),
                    titleStyle = MaterialTheme.typography.titleMedium,
                    actionIcon = Icons.Rounded.Close,
                    actionIconContentDescription = "Action icon",
                    modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                    onActionClick = { onDismissRequest() }
                )
                StoryListForClueContent(
                    modifier = Modifier.wrapContentSize().padding(vertical = 8.dp),
                    sources = sources,
                    onSourceClicked = { articleSource ->
                        webViewController.open(url = articleSource.articleLink)
                    }
                )
            }
        }
    }
}

@Composable
private fun StoryListForClueContent(
    modifier: Modifier,
    sources: List<Article>,
    onSourceClicked: (Article) -> Unit,
) {
    Box(modifier = modifier) {
        SourcesBottomList(
            modifier = Modifier.wrapContentSize(),
            sources = sources,
            onSourceClicked = { onSourceClicked(it) },
        )
    }
}

@Composable
fun SourcesBottomList(
    modifier: Modifier = Modifier,
    sources: List<Article>,
    onSourceClicked: (Article) -> Unit,
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = 8.dp),
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
) {
    Box(modifier = modifier.wrapContentSize()) {
        val scrollableState = rememberLazyListState()
        LazyColumn(
            verticalArrangement = verticalArrangement,
            horizontalAlignment = horizontalAlignment,
            state = scrollableState,
        ) {
            sources.forEach { articleSource ->
                val articleSourceId = articleSource.articleId
                item(key = articleSourceId) {
                    SourceItem(
                        title = articleSource.articleTitle,
                        imageUrl = articleSource.articleImageUrl ?: "",
                        thumbnailName = articleSource.sourceTitle,
                        thumbnailUrl = articleSource.sourceImageUrl ?: "",
                        onClick = { onSourceClicked(articleSource) },
                    )
                }
            }
        }
    }
}
