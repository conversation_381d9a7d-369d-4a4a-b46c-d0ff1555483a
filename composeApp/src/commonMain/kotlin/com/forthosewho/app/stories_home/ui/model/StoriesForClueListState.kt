package com.forthosewho.app.stories_home.ui.model

import androidx.paging.PagingData
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.stories_home.domain.model.Story
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow

object StoriesForClueListState {
    data class State(
        val screenState: ScreenState = ScreenState.StateInitial(),
        val currentStory: Story? = null,
        val userClues: List<ClueView> = emptyList(),
        val stories: Flow<PagingData<Story>> = emptyFlow(),
        val error: Throwable? = null
    )

    sealed class ScreenState {
        data class StateInitial(val subtitle: String = String.empty()) : ScreenState()
        data object StateNavigateBack : ScreenState()
    }
}
