package com.forthosewho.app.stories_home.ui.model

import androidx.paging.PagingData
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.stories_home.domain.model.Story
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow

object StoriesListState {
    data class State(
        val screenState: ScreenState = ScreenState.Initial,
        val currentStory: Story? = null,
        val stories: Flow<PagingData<Story>> = emptyFlow(),
        val userClues: List<ClueView> = emptyList(),
        val isRefreshing: Boolean = false,
        val error: Throwable? = null
    )

    sealed class ScreenState {
        data object Initial : ScreenState()
        data object StoriesLoading : ScreenState()
        data object StoriesLoaded : ScreenState()
        data class NavigateToStoriesForClues(val clueView: ClueView) : ScreenState()
        data object NavigateToLogin : ScreenState()
        data object NavigateBack : ScreenState()
    }
}
