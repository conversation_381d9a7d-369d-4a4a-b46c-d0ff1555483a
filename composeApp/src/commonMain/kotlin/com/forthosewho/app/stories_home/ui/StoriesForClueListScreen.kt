package com.forthosewho.app.stories_home.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.assistant.ui.AssistantBottomSheetDialog
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.platform.utils.LazyPagingItems
import com.forthosewho.app.platform.utils.collectAsLazyPagingItems
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.ui.model.StoriesForClueListState
import com.forthosewho.app.stories_home.ui.viewmodel.StoriesForClueViewModel
import com.forthosewho.app.theme.bottomsheets.rememberSheetController
import com.forthosewho.app.theme.cards.BuildingFeedCard
import com.forthosewho.app.theme.cards.StoryCard
import com.forthosewho.app.theme.icon.FtwIcons.BackArrow
import com.forthosewho.app.theme.toolbars.StoriesWithSubtitleTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.stories_focused_on_a_clue
import forthosewho_app.composeapp.generated.resources.this_story_was_picked_because
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel
import org.koin.core.parameter.parametersOf

const val CLUE_ID: String = "clueId"

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun StoriesForClueListScreen(
    storiesViewModel: StoriesForClueViewModel = koinViewModel(parameters = { parametersOf(CLUE_ID) }),
    listState: LazyListState = rememberLazyListState(),
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    scrollBehavior: TopAppBarScrollBehavior = TopAppBarDefaults.pinnedScrollBehavior(),
    onLogout: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val state by storiesViewModel.state.collectAsState()

    var subtitleDescription by remember { mutableStateOf(String.empty()) }

    val sourcesSheetController = rememberSheetController()
    val cluesForStorySheetController = rememberSheetController()
    val assistantSheetController = rememberSheetController()


    Scaffold(
        modifier = Modifier.fillMaxSize().nestedScroll(connection = scrollBehavior.nestedScrollConnection),
        topBar = {
            StoriesWithSubtitleTopAppBar(
                modifier = Modifier.fillMaxWidth().padding(top = 72.dp, start = 20.dp, end = 20.dp),
                title = stringResource(Res.string.stories_focused_on_a_clue),
                subtitle = subtitleDescription,
                leftIconButton = BackArrow,
                leftIconButtonContentDescription = "Back button",
                onLeftIconClicked = { storiesViewModel.onBackPressed() },
            )
        },
        snackbarHost = {
            SnackbarHost(
                modifier = Modifier.testTag(tag = ErrorBarDefaults.ErrorTestTag).navigationBarsPadding(),
                hostState = snackbarHostState,
            )
        },
        contentWindowInsets = WindowInsets.ime,
    ) { paddingValues ->
        StoryListForClueContent(
            modifier = Modifier.fillMaxSize().padding(paddingValues = paddingValues),
            viewModel = storiesViewModel,
            listState = listState,
            onStoryLabelClicked = { story ->
                storiesViewModel.selectStory(story)
                sourcesSheetController.show()
            },
            onClueLabelClicked = { story ->
                storiesViewModel.selectStory(story)
                cluesForStorySheetController.show()
            }
        )
        when (state.screenState) {
            is StoriesForClueListState.ScreenState.StateInitial -> {
                subtitleDescription = (state.screenState as StoriesForClueListState.ScreenState.StateInitial).subtitle
            }

            is StoriesForClueListState.ScreenState.StateNavigateBack -> {
                onBackPressed()
            }
        }
    }
    state.currentStory?.let { story ->
        SourcesBottomSheetDialog(
            modifier = Modifier,
            sources = story.sourcesList,
            modalSheetState = sourcesSheetController.state,
            onDismissRequest = {
                sourcesSheetController.hide()
            },
        )
    }
    state.currentStory?.let { story ->
        CluesBottomSheetDialog(
            modifier = Modifier,
            title = stringResource(Res.string.this_story_was_picked_because),
            cluesList = story.relevantClues.map { ClueView.from(clue = it) },
            modalSheetState = cluesForStorySheetController.state,
            onEditButtonClicked = {
                assistantSheetController.show()
                cluesForStorySheetController.hide()
            },
            onDismissRequest = { cluesForStorySheetController.hide() },
        )
    }
    AssistantBottomSheetDialog(
        category = InitializeThreadCategory.CHANGE_CLUES,
        modalSheetState = assistantSheetController.state,
        onDismissRequest = { assistantSheetController.hide() },
    )
}

@Composable
fun StoryListForClueContent(
    modifier: Modifier,
    viewModel: StoriesForClueViewModel,
    listState: LazyListState,
    onStoryLabelClicked: (Story) -> Unit,
    onClueLabelClicked: (Story) -> Unit,
    content: @Composable () -> Unit = {},
) {
    content()
    val storiesCollected = viewModel.state.value.stories.collectAsLazyPagingItems()

    Box(modifier = modifier) {
        if (storiesCollected.itemCount == 0) {
            BuildingFeedCard(
                modifier = Modifier.fillMaxSize().padding(16.dp),
                refreshFeedAction = { viewModel.refresh() })
        } else {
            StoriesForCluePaginatedList(
                modifier = Modifier.fillMaxSize(),
                state = listState,
                stories = storiesCollected,
                onStoryLabelClicked = { story -> onStoryLabelClicked(story) },
                onClueLabelClicked = { story -> onClueLabelClicked(story) },
                onError = viewModel::onError,
            )
        }
    }
}

@Composable
internal fun StoriesForCluePaginatedList(
    stories: LazyPagingItems<Story>,
    onError: (error: Throwable) -> Unit,
    modifier: Modifier = Modifier,
    onStoryLabelClicked: (Story) -> Unit,
    onClueLabelClicked: (Story) -> Unit,
    state: LazyListState = rememberLazyListState(),
    contentPadding: PaddingValues = PaddingValues(all = 16.dp),
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = 16.dp),
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
) {
    val itemModifier = Modifier.fillMaxWidth().wrapContentHeight()

    LazyColumn(
        modifier = modifier,
        state = state,
        contentPadding = contentPadding,
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
    ) {
        items(count = stories.itemCount) { index ->
            stories[index]?.run {
                StoryCard(
                    story = this,
                    modifier = itemModifier,
                    onStoryLabelClicked = onStoryLabelClicked,
                    onClueLabelClicked = onClueLabelClicked,
                )
            }
        }
    }

    LaunchedEffect(stories.loadState) {
        when {
            stories.loadState.refresh is LoadState.Error -> onError((stories.loadState.refresh as LoadState.Error).error)
            stories.loadState.append is LoadState.Error -> onError((stories.loadState.append as LoadState.Error).error)
        }
    }
}

