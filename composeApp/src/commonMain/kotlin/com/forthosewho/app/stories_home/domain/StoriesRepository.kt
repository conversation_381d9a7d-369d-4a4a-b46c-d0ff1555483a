package com.forthosewho.app.stories_home.domain

import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.platform.data.PaginatedData
import com.forthosewho.app.stories_home.domain.model.Story
import com.github.michaelbull.result.Result

interface StoriesRepository {
    suspend fun getStories(
        userId: String,
        limit: Int,
        offset: Int,
        token: String,
        clueIds: List<String>
    ): Result<PaginatedData<Story>,ErrorResponse>
}