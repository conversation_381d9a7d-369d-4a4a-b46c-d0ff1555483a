package com.forthosewho.app.stories_home.domain.model

import com.forthosewho.app.account.domain.model.Clue

data class Story(
    val storyId: String,
    val storyTitle: String,
    val storyDescription: String,
    val storyUrlList: List<String>,
    val sourcesList: List<Article>,
    val relevantClues: List<Clue>,
    val rating: String,
    val value: String,
    val id: String,
    val profile: String,
    val status: String,
    val clue: String,
    val clueAlt: String,
) {
    companion object {
        fun Story.isStoryValid(): Boolean {
            return storyId.isNotEmpty() && storyTitle.isNotEmpty() && storyDescription.isNotEmpty()
        }

        fun Story.isHavingArticleImage(): Boolean {
            return storyUrlList.isNotEmpty()
        }

        fun Story.getNextUrl(currentUrl: String): String {
            val position = storyUrlList.indexOf(element = currentUrl)
            return if (storyUrlList.size <= position) {
                storyUrlList.get(index = position.plus(1))
            } else {
                storyUrlList.get(index = 0)
            }
        }
    }
}
