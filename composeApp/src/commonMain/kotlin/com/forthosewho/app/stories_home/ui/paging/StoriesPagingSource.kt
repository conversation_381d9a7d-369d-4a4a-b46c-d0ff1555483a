package com.forthosewho.app.stories_home.ui.paging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.forthosewho.app.platform.data.PaginatedData
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.domain.usecase.GetStoriesUseCase
import com.forthosewho.app.stories_home.ui.viewmodel.PAGE_SIZE_LIMIT

internal class StoriesPagingSource(
    private val useCase: GetStoriesUseCase,
    private val clueIds: List<String> = listOf()
) : PagingSource<Int, Story>() {

    override fun getRefreshKey(state: PagingState<Int, Story>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(state.config.pageSize) ?: anchorPage?.nextKey?.minus(state.config.pageSize)
        }
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Story> {
        return try {
            //Napier.e { "----------------> load(params loadsize = ${params.loadSize})" }
            val offset = params.key ?: 0 // Start at offset 0 if undefined
            runCatching {
                loadPage(page = offset)
            }.fold(
                onSuccess = { result ->
                    LoadResult.Page(
                        data = result.data,
                        prevKey = if (offset == 0) null else offset.minus(other = PAGE_SIZE_LIMIT),
                        nextKey = if (result.data.isEmpty()) null else offset.plus(other = PAGE_SIZE_LIMIT)
                    )
                },
                onFailure = { e -> LoadResult.Error(throwable = e) }
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    private suspend fun loadPage(page: Int): PaginatedData<Story> {
        return useCase.invoke(param = GetStoriesUseCase.Params(limit = PAGE_SIZE_LIMIT, offset = page, clueIds = clueIds)).value
    }
}