package com.forthosewho.app.stories_home.data.entitiy

import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.platform.data.PaginatedData
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.platform.utils.tokeniseClues
import com.forthosewho.app.stories_home.domain.model.Article
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.domain.model.Story.Companion.isStoryValid
import com.forthosewho.app.stories_home.ui.viewmodel.PAGE_SIZE_LIMIT
import kotlinx.serialization.Serializable

@Serializable
data class StoriesResponseRaw(
    private val rows: List<StoryRaw>,
) {

    fun toDomain(): PaginatedData<Story> = PaginatedData(
        data = rows.map { it.toStory() }.filter { it.isStoryValid() },
        limit = PAGE_SIZE_LIMIT,
        offset = rows.size
    )

    companion object {

        fun emptyPaginatedData(): PaginatedData<Story> = PaginatedData(
            data = emptyList(),
            limit = PAGE_SIZE_LIMIT,
            offset = 0
        )
    }
}

@Serializable
data class StoryRaw(
    private val storyId: String? = null,
    private val createdAt: String? = null,
    private val updatedAt: String? = null,
    private val title: String? = null,
    private val content: String? = null,
    private val storyImageUrl: String? = null,
    private val articles: List<ArticleRaw> = emptyList(),
    private val relevantClues: String? = null,
    private val rating: String? = null,
    private val value: String? = null,
    private val id: String? = null,
    private val profile: String? = null,
    private val status: String? = null,
    private val clue: String? = null,
    private val clueAlt: String? = null,
) {

    fun toStory() = Story(
        storyId = storyId ?: String.empty(),
        storyTitle = title ?: String.empty(),
        storyDescription = content ?: String.empty(),
        storyUrlList = articles.mapNotNull { it.toArticle().articleImageUrl }.filter { it.isNotEmpty() }
            .filter { it.isNotBlank() },
        sourcesList = articles.map { it.toArticle() },
        relevantClues = relevantClues?.tokeniseClues()?.map { Clue(clue = it) } ?: listOf(),
        rating = rating ?: String.empty(),
        value = value ?: String.empty(),
        id = id ?: String.empty(),
        profile = profile ?: String.empty(),
        status = status ?: String.empty(),
        clue = clue ?: String.empty(),
        clueAlt = clueAlt ?: String.empty(),
    )
}

@Serializable
data class ArticleRaw(
    private val articleId: String? = null,
    private val createdAt: String? = null,
    private val publishedOn: String? = null,
    private val content: String? = null,
    private val link: String? = null,
    private val title: String? = null,
    private val articleImageUrl: String? = null,
    private val sourceImageUrl: String? = null,
    private val source: String? = null,
) {

    fun toArticle() = Article(
        articleId = articleId ?: String.empty(),
        articleTitle = title ?: String.empty(),
        articleDescription = content ?: String.empty(),
        articleImageUrl = articleImageUrl ?: String.empty(),
        articleLink = link ?: String.empty(),
        sourceTitle = source ?: String.empty(),
        sourceImageUrl = sourceImageUrl ?: String.empty(),
    )
}
