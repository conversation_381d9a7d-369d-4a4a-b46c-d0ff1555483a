package com.forthosewho.app.batcave.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.forthosewho.app.batcave.ui.model.BatcaveState
import com.forthosewho.app.batcave.ui.viewmodel.BatcaveViewModel
import com.forthosewho.app.login.ui.ButtonTemplate
import com.forthosewho.app.theme.icon.FtwIcons
import com.forthosewho.app.theme.toolbars.StoriesTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.batcave
import forthosewho_app.composeapp.generated.resources.saveAndRestart
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
fun BatcaveScreen(
    batcaveViewModel: BatcaveViewModel = koinViewModel<BatcaveViewModel>(),
    onNavigateToStart: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val state by batcaveViewModel.state.collectAsState()

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            StoriesTopAppBar(
                title = stringResource(Res.string.batcave),
                navigationIcon = FtwIcons.BackArrow,
                navigationIconContentDescription = "Action icon",
                onNavigationClick = onBackPressed,
                modifier = Modifier.fillMaxWidth(),
            )
        },
        contentWindowInsets = WindowInsets.ime,
    ) { paddingValues ->

        // Handle state change inside LaunchedEffect to trigger only once
        LaunchedEffect(state.screenState) {
            if (state.screenState is BatcaveState.ScreenState.StateInitial) {
                batcaveViewModel.onStateInitial()
            } else if (state.screenState is BatcaveState.ScreenState.StateRestart) {
                onNavigateToStart()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 16.dp, end = 16.dp, bottom = 60.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                modifier = Modifier.wrapContentHeight(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("Productional Environment:", modifier = Modifier.weight(1f))
                Switch(
                    checked = state.isProductional,  // ✅ Directly use ViewModel state
                    onCheckedChange = {
                        batcaveViewModel.onSwitchInteracted(it)
                    }
                )
            }
            Spacer(modifier = Modifier.weight(.1F, fill = true))
            ButtonTemplate(
                modifier = Modifier.wrapContentHeight(),
                enabled = true,
                buttonText = stringResource(Res.string.saveAndRestart),
                onClick = { batcaveViewModel.onRestartButtonClicked() }
            )
        }
    }
}
