package com.forthosewho.app.batcave.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.batcave.ui.model.BatcaveState
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.ClearStoredValuesUseCase
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class BatcaveViewModel(
    private val setStoredValueUseCase: SetStoredValueUseCase = SetStoredValueUseCase(),
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val clearStoredValuesUseCase: ClearStoredValuesUseCase = ClearStoredValuesUseCase(),
) : ViewModel() {
    private val _state = MutableStateFlow(BatcaveState.State.createInitialState())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        viewModelScope.launch {
            val isProductional = !getStoredValueUseCase.getBoolean(key = DataStoreValues.IS_STAGING)
            _state.update { state -> state.copy(isProductional = isProductional) }
        }
    }

    fun onSwitchInteracted(switch: Boolean) {
        _state.update { state -> state.copy(isProductional = switch) }
    }

    fun onRestartButtonClicked() {
        viewModelScope.launch {
            clearStoredValuesUseCase.invoke()
            viewModelScope.launch {
                setStoredValueUseCase.apply {
                    invoke(
                        SetStoredValueUseCase.Params(
                            dataStoreValue = DataStoreValues.IS_STAGING,
                            value = !_state.value.isProductional
                        )
                    )
                }
            }
            _state.update { state ->
                state.copy(screenState = BatcaveState.ScreenState.StateRestart)
            }
        }
    }
}