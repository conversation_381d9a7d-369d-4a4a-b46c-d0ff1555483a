package com.forthosewho.app.batcave.ui.model

class BatcaveState {

    data class State(
        val screenState: ScreenState = ScreenState.StateInitial,
        val isProductional: Boolean = false
    ) {
        companion object {
            fun createInitialState(): State = State(screenState = ScreenState.StateInitial)
        }
    }

    sealed class ScreenState {
        data object StateInitial : ScreenState()
        data object StateRestart : ScreenState()
    }
}
