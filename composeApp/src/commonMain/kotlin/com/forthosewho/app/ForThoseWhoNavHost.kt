package com.forthosewho.app

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.forthosewho.app.navigation.BottomNavigationItems
import com.forthosewho.app.navigation.FtwNavigationGraph
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Grey
import com.forthosewho.app.theme.toolbars.BottomNavigationBar
import com.forthosewho.app.theme.toolbars.BottomNavigationBarItem

@Composable
fun ForThoseWhoNavHost(navController: NavHostController = rememberNavController()) {

    var toolbarVisible by remember { mutableStateOf(true) }
    var buttonsVisible by remember { mutableStateOf(true) }

    val screens = listOf(
        BottomNavigationItems.Stories,
        BottomNavigationItems.Settings
    )

    Scaffold(
        bottomBar = {
            if (buttonsVisible) {
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                val currentRoute = navBackStackEntry?.destination?.route

                BottomNavigationBar {
                    screens.forEachIndexed { index, screen ->
                        BottomNavigationBarItem(
                            icon = {
                                Icon(
                                    imageVector = screen.icon,
                                    contentDescription = "",
                                    tint = Grey
                                )
                            },
                            selectedIcon = {
                                Icon(
                                    imageVector = screen.icon,
                                    contentDescription = "item",
                                    tint = Black
                                )
                            },
                            label = { screens[index].title },
                            selected = currentRoute == screen.route,
                            onClick = {
                                navController.navigate(screen.route) {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            },
                        )
                    }
                }
            }
        },
        contentWindowInsets = WindowInsets.ime,
    ) { paddingValues ->
        Box(modifier = Modifier.padding(paddingValues)) {
            FtwNavigationGraph(
                navController = navController,
                onBottomBarVisibilityChanged = { buttonsVisible = it },
                onTopBarVisibilityChanged = { toolbarVisible = it },
            )
        }
    }
}
