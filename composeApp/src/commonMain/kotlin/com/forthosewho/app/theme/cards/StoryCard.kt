package com.forthosewho.app.theme.cards

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.domain.model.Story.Companion.getNextUrl
import com.forthosewho.app.stories_home.domain.model.Story.Companion.isHavingArticleImage
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Grey
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.icon.FtwIcons.Dots
import com.mikepenz.markdown.m3.Markdown
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.coil3.CoilImage
import org.intellij.markdown.flavours.commonmark.CommonMarkFlavourDescriptor

@Composable
fun StoryCard(
    story: Story,
    modifier: Modifier,
    onStoryLabelClicked: (story: Story) -> Unit,
    onClueLabelClicked: (story: Story) -> Unit,
) {
    Card(
        modifier = modifier.fillMaxWidth().wrapContentHeight(),
        colors = CardColors(
            contentColor = Black,
            containerColor = Beige,
            disabledContainerColor = Beige,
            disabledContentColor = Beige
        )
    ) {
        if (story.isHavingArticleImage()) {
            CoilImage(
                modifier = Modifier.fillMaxWidth().height(210.dp),
                imageModel = { story.getNextUrl(story.storyUrlList[0]) }, // loading a network image or local resource using an URL.
                imageOptions = ImageOptions(
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.Center,
                    contentDescription = story.storyTitle,
                )
            )
        }
        Column(
            modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(16.dp),
        ) {
            StoryCardTitle(
                storyTitle = story.storyTitle,
                modifier = Modifier,
            )
            Spacer(
                modifier = Modifier.height(16.dp)
            )
            StoryShortDescription(
                storyShortDescription = story.storyDescription
            )
            Spacer(
                modifier = Modifier.height(16.dp)
            )
            StorySourceInformation(
                modifier = Modifier,
                storyThumbnailList = story.sourcesList.mapNotNull { it.sourceImageUrl },
                onStorySourcesClicked = { onStoryLabelClicked(story) },
                onStoryCluesClicked = { onClueLabelClicked(story) }
            )
        }
    }
}

@Composable
fun StoryCardTitle(
    storyTitle: String,
    modifier: Modifier = Modifier,
) {
    Markdown(
        modifier = modifier.fillMaxWidth().wrapContentHeight(),
        content = storyTitle,
        flavour = CommonMarkFlavourDescriptor()
    )
}

@Composable
fun StoryShortDescription(
    storyShortDescription: String,
) {
    Markdown(
        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
        content = storyShortDescription,
        flavour = CommonMarkFlavourDescriptor()
    )
}

@Composable
fun StorySourceInformation(
    storyThumbnailList: List<String> = listOf(),
    modifier: Modifier = Modifier,
    onStorySourcesClicked: () -> Unit,
    onStoryCluesClicked: () -> Unit
) {
    Row(
        modifier = modifier.fillMaxWidth().wrapContentHeight().padding(top = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        StorySourceThumbnail(
            modifier = modifier.clickable { onStorySourcesClicked() },
            thumbnailList = storyThumbnailList.take(n = 3),
            fullList = storyThumbnailList.count(),
        )
        IconButton(
            modifier = Modifier.size(36.dp),
            onClick = { onStoryCluesClicked() },
            enabled = true,
        ) {
            Icon(
                modifier = Modifier.size(24.dp),
                imageVector = Dots,
                contentDescription = null,
                tint = Black,
            )
        }
    }
}

@Composable
fun StorySourceThumbnail(
    modifier: Modifier,
    thumbnailList: List<String>,
    fullList: Int,
) {
    val thumbnailSize = 24.dp
    val thumbnailSpacing = 12.dp
    val extraSpacing = 10.dp
    val baseOffset = (thumbnailList.size * thumbnailSize) - ((thumbnailList.size - 1) * thumbnailSpacing) + extraSpacing
    val offset = if (fullList > 3) baseOffset + thumbnailSize else baseOffset
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.width(offset).border(
            width = 1.dp,
            color = Grey,
            shape = CircleShape
        )
    ) {
        Box(modifier = Modifier.wrapContentWidth()) {
            for (indices in thumbnailList.indices) {
                val item = thumbnailList[indices]
                CoilImage(
                    modifier = Modifier.padding(5.dp).size(24.dp).offset(x = indices * 12.dp).border(
                        width = 1.dp,
                        color = Grey,
                        shape = CircleShape
                    ).clip(CircleShape).background(Color.White),
                    imageModel = { item },
                    imageOptions = ImageOptions(
                        contentScale = ContentScale.Crop,
                        alignment = Alignment.Center
                    )
                )
            }
        }
        if (fullList > 3) {
            StorySmallLabel(
                labelText = "+" + (fullList - 3),
                modifier = Modifier.weight(1F).wrapContentHeight().padding(top = 1.dp, start = 12.dp),
            )
        }
    }
}

@Composable
fun StorySmallLabel(
    labelText: String,
    modifier: Modifier,
) {
    Text(
        text = labelText,
        style = MaterialTheme.typography.labelMedium,
        textAlign = TextAlign.Start,
        color = GreyLight,
        modifier = modifier.wrapContentWidth().wrapContentHeight(),
    )
}

