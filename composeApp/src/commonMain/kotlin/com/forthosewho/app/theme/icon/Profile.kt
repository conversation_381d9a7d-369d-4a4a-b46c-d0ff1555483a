package com.forthosewho.app.theme.icon

import androidx.compose.material.icons.Icons
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val Icons.Profile: ImageVector
    get() {
        if (_profile != null) {
            return _profile!!
        }
        _profile = Builder(
            name = "Profile", defaultWidth = 74.0.dp, defaultHeight = 55.0.dp,
            viewportWidth = 74.0f, viewportHeight = 55.0f
        ).apply {
            path(
                fill = SolidColor(Color(0xFF979BB1)), stroke = null, strokeLineWidth = 0.0f,
                strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                pathFillType = NonZero
            ) {
                moveTo(34.25f, 30.75f)
                curveTo(38.7969f, 30.75f, 42.5f, 34.4531f, 42.5f, 39.0f)
                curveTo(42.5f, 39.8438f, 41.7969f, 40.5f, 41.0f, 40.5f)
                horizontalLineTo(23.0f)
                curveTo(22.1562f, 40.5f, 21.5f, 39.8438f, 21.5f, 39.0f)
                curveTo(21.5f, 34.4531f, 25.1562f, 30.75f, 29.75f, 30.75f)
                horizontalLineTo(34.25f)
                close()
                moveTo(23.75f, 38.25f)
                horizontalLineTo(40.2031f)
                curveTo(39.8281f, 35.2969f, 37.2969f, 33.0f, 34.25f, 33.0f)
                horizontalLineTo(29.75f)
                curveTo(26.6562f, 33.0f, 24.125f, 35.2969f, 23.75f, 38.25f)
                close()
                moveTo(32.0f, 28.5f)
                curveTo(28.6719f, 28.5f, 26.0f, 25.8281f, 26.0f, 22.5f)
                curveTo(26.0f, 19.2188f, 28.6719f, 16.5f, 32.0f, 16.5f)
                curveTo(35.2812f, 16.5f, 38.0f, 19.2188f, 38.0f, 22.5f)
                curveTo(38.0f, 25.8281f, 35.2812f, 28.5f, 32.0f, 28.5f)
                close()
                moveTo(32.0f, 18.75f)
                curveTo(29.8906f, 18.75f, 28.25f, 20.4375f, 28.25f, 22.5f)
                curveTo(28.25f, 24.6094f, 29.8906f, 26.25f, 32.0f, 26.25f)
                curveTo(34.0625f, 26.25f, 35.75f, 24.6094f, 35.75f, 22.5f)
                curveTo(35.75f, 20.4375f, 34.0625f, 18.75f, 32.0f, 18.75f)
                close()
            }
            path(
                fill = SolidColor(Color(0xFF979BB1)), stroke = null, strokeLineWidth = 0.0f,
                strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                pathFillType = NonZero
            ) {
                moveTo(47.4414f, 23.6016f)
                lineTo(49.0f, 23.0f)
                lineTo(49.5742f, 21.4688f)
                curveTo(49.6016f, 21.332f, 49.7383f, 21.25f, 49.875f, 21.25f)
                curveTo(49.9844f, 21.25f, 50.1211f, 21.332f, 50.1484f, 21.4688f)
                lineTo(50.75f, 23.0f)
                lineTo(52.2812f, 23.6016f)
                curveTo(52.418f, 23.6289f, 52.5f, 23.7656f, 52.5f, 23.875f)
                curveTo(52.5f, 24.0117f, 52.418f, 24.1484f, 52.2812f, 24.1758f)
                lineTo(50.75f, 24.75f)
                lineTo(50.1484f, 26.3086f)
                curveTo(50.1211f, 26.418f, 49.9844f, 26.5f, 49.875f, 26.5f)
                curveTo(49.7383f, 26.5f, 49.6016f, 26.418f, 49.5742f, 26.3086f)
                lineTo(49.0f, 24.75f)
                lineTo(47.4414f, 24.1758f)
                curveTo(47.332f, 24.1484f, 47.25f, 24.0117f, 47.25f, 23.875f)
                curveTo(47.25f, 23.7656f, 47.332f, 23.6289f, 47.4414f, 23.6016f)
                close()
                moveTo(44.1055f, 23.2734f)
                lineTo(45.5273f, 26.3906f)
                lineTo(48.6445f, 27.8125f)
                curveTo(48.8086f, 27.8945f, 48.918f, 28.0586f, 48.918f, 28.2227f)
                curveTo(48.918f, 28.3867f, 48.8086f, 28.5508f, 48.6445f, 28.6055f)
                lineTo(45.5273f, 30.0547f)
                lineTo(44.1055f, 33.1719f)
                curveTo(44.0234f, 33.3359f, 43.8594f, 33.4453f, 43.6953f, 33.4453f)
                curveTo(43.5312f, 33.4453f, 43.3672f, 33.3359f, 43.3125f, 33.1719f)
                lineTo(41.8633f, 30.0547f)
                lineTo(38.7461f, 28.6328f)
                curveTo(38.582f, 28.5508f, 38.5f, 28.3867f, 38.5f, 28.2227f)
                curveTo(38.5f, 28.0586f, 38.582f, 27.8945f, 38.7461f, 27.8125f)
                lineTo(41.8633f, 26.3906f)
                lineTo(43.3125f, 23.2734f)
                curveTo(43.3672f, 23.1094f, 43.5312f, 23.0f, 43.6953f, 23.0f)
                curveTo(43.8594f, 23.0f, 44.0234f, 23.1094f, 44.1055f, 23.2734f)
                close()
                moveTo(49.0f, 31.75f)
                lineTo(49.5742f, 30.2188f)
                curveTo(49.6016f, 30.082f, 49.7383f, 30.0f, 49.875f, 30.0f)
                curveTo(49.9844f, 30.0f, 50.1211f, 30.082f, 50.1484f, 30.2188f)
                lineTo(50.75f, 31.75f)
                lineTo(52.2812f, 32.3516f)
                curveTo(52.418f, 32.3789f, 52.5f, 32.5156f, 52.5f, 32.625f)
                curveTo(52.5f, 32.7617f, 52.418f, 32.8984f, 52.2812f, 32.9258f)
                lineTo(50.75f, 33.5f)
                lineTo(50.1484f, 35.0586f)
                curveTo(50.1211f, 35.168f, 49.9844f, 35.25f, 49.875f, 35.25f)
                curveTo(49.7383f, 35.25f, 49.6016f, 35.168f, 49.5742f, 35.0586f)
                lineTo(49.0f, 33.5f)
                lineTo(47.4414f, 32.9258f)
                curveTo(47.332f, 32.8984f, 47.25f, 32.7617f, 47.25f, 32.625f)
                curveTo(47.25f, 32.5156f, 47.332f, 32.3789f, 47.4414f, 32.3516f)
                lineTo(49.0f, 31.75f)
                close()
            }
        }
            .build()
        return _profile!!
    }

private var _profile: ImageVector? = null
