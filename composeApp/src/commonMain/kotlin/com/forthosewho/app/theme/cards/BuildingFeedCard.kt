package com.forthosewho.app.theme.cards

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.forthosewho.app.theme.buttons.MainClickToActionButton
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Cyan
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.building_your_feed_subtitle
import forthosewho_app.composeapp.generated.resources.building_your_feed_title
import forthosewho_app.composeapp.generated.resources.refresh_feed
import org.jetbrains.compose.resources.stringResource
import kotlinx.coroutines.delay
import androidx.compose.runtime.LaunchedEffect

@Composable
fun BuildingFeedCard(
    modifier: Modifier,
    isLoading: Boolean = false,
    refreshFeedAction: () -> Unit = {},
) {
    // State to track temporary loading
    var loadingVisible by remember { mutableStateOf(isLoading) }
    // Effect to handle temporary loading duration
    LaunchedEffect(loadingVisible) {
        if (loadingVisible && !isLoading) {
            delay(1500) // 1.5 seconds
            loadingVisible = false
        }
    }
    // Update loading state when isLoading prop changes
    LaunchedEffect(isLoading) {
        loadingVisible = isLoading
    }
    Card(
        modifier = modifier,
        colors = CardColors(
            contentColor = Black,
            containerColor = Beige,
            disabledContainerColor = Beige,
            disabledContentColor = Beige
        )
    ) {
        Column(
            modifier = Modifier.fillMaxSize().padding(16.dp),
            horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(Res.string.building_your_feed_title),
                style = MaterialTheme.typography.displayMedium,
                textAlign = TextAlign.Center,
                color = Black,
                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 22.dp),
            )
            Spacer(
                modifier = Modifier.height(16.dp)
            )
            Text(
                text = stringResource(Res.string.building_your_feed_subtitle),
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = 26.sp,
                textAlign = TextAlign.Center,
                color = Black,
                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 22.dp),
            )
            Spacer(
                modifier = Modifier.height(32.dp)
            )
            MainClickToActionButton(
                buttonText = stringResource(Res.string.refresh_feed),
                modifier = Modifier.wrapContentHeight().wrapContentWidth(),
                colors = ButtonColors(
                    containerColor = Cyan,
                    contentColor = Blue,
                    disabledContainerColor = Cyan,
                    disabledContentColor = Blue,
                ),
                isLoading = loadingVisible,
                buttonTextColor = Blue,
                onButtonClick = {
                    refreshFeedAction()
                    loadingVisible = true // Start loading when button is clicked
                }
            )
        }
    }
}
