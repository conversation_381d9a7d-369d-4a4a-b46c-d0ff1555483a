package com.forthosewho.app.theme.colors

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color

val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    onPrimary = Purple20,
    primaryContainer = Purple30,
    onPrimaryContainer = Purple90,
    secondary = Orange80,
    onSecondary = Orange20,
    secondaryContainer = Orange30,
    onSecondaryContainer = Orange90,
    tertiary = Blue80,
    onTertiary = Blue20,
    tertiaryContainer = Blue30,
    onTertiaryContainer = Blue90,
    error = Red80,
    onError = Red20,
    errorContainer = Red30,
    onErrorContainer = Red90,
    background = DarkPurpleGray10,
    onBackground = DarkPurpleGray90,
    surface = DarkPurpleGray10,
    onSurface = DarkPurpleGray90,
    surfaceVariant = PurpleGray30,
    onSurfaceVariant = PurpleGray80,
    inverseSurface = DarkPurpleGray90,
    inverseOnSurface = DarkPurpleGray10,
    outline = PurpleGray60,
)

val LightColorScheme = lightColorScheme(
    onSecondary = White,
    secondaryContainer = Black,
    background = White,
    onBackground = Black,
    surface = White,
    onSurface = Black,
    onPrimary = White,
    onTertiary = White,
    onError = White,
    primary = Blue,
    onSurfaceVariant = GreyLight,
    onPrimaryContainer = Black,
    primaryContainer = Color.Transparent,
    outline = Beige,
    secondary = Beige,
    onSecondaryContainer = GreyLight,
    surfaceVariant = Beige,

    tertiary = Teal40,
    tertiaryContainer = Teal90,
    onTertiaryContainer = Teal10,
    error = Red40,
    errorContainer = Red90,
    onErrorContainer = Red10,
    inverseSurface = DarkGreenGray20,
    inverseOnSurface = DarkGreenGray95,
)

/**
 * A [CompositionLocal] that provides the light theme color palette.
 */

val LocalAppColors = staticCompositionLocalOf { LightColorScheme }

val LocalAppDarkColors = staticCompositionLocalOf { DarkColorScheme }
