package com.forthosewho.app.theme.buttons

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.White

@Composable
fun MainClickToActionButton(
    modifier: Modifier = Modifier,
    buttonText: String,
    buttonTextColor: Color = White,
    shape: RoundedCornerShape = RoundedCornerShape(40.dp),
    enabled: Boolean = true,
    colors: ButtonColors = ButtonColors(
        containerColor = Blue,
        contentColor = White,
        disabledContainerColor = Blue,
        disabledContentColor = GreyLight,
    ),
    icon: ImageVector? = null,
    isLoading: Boolean = false, // Add loading state parameter,
    onButtonClick: () -> Unit,
) {
    Button(
        onClick = { onButtonClick() },
        modifier = modifier,
        shape = shape,
        colors = colors,
        enabled = enabled && !isLoading, // Disable button when loading
        contentPadding = PaddingValues(16.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (isLoading) {
                // Show loading indicator
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = buttonTextColor,
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            icon?.let {
                Icon(
                    modifier = Modifier
                        .size(24.dp)
                        .padding(bottom = 8.dp),
                    imageVector = icon,
                    contentDescription = "Check icon",
                    tint = buttonTextColor
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            Text(
                text = buttonText.uppercase(),
                color = buttonTextColor,
                style = MaterialTheme.typography.headlineMedium
            )
        }
    }
}
