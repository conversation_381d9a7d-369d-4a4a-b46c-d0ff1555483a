package com.forthosewho.app.theme.bottomsheets

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.composables.core.ModalBottomSheetState
import com.composables.core.SheetDetent
import com.composables.core.SheetDetent.Companion.FullyExpanded
import com.composables.core.SheetDetent.Companion.Hidden
import com.forthosewho.app.theme.transitions.tweenFtwDefault
import com.composables.core.rememberModalBottomSheetState
import kotlinx.coroutines.CoroutineScope
import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.launch

@Composable
fun rememberFtwModalBottomSheetState(
    initialDetent: SheetDetent = Hidden,
    detents: List<SheetDetent> = listOf(Hidden, FullyExpanded),
    velocityThreshold: () -> Dp = { 125.dp },
    positionalThreshold: (totalDistance: Dp) -> Dp = { 56.dp },
    confirmDetentChange: (SheetDetent) -> Boolean = { true },
): ModalBottomSheetState {
    return rememberModalBottomSheetState(
        animationSpec = tweenFtwDefault(),
        initialDetent = initialDetent,
        detents = detents,
        velocityThreshold = velocityThreshold,
        positionalThreshold = positionalThreshold,
        confirmDetentChange = confirmDetentChange
    )
}

@Composable
fun rememberFtwExpandedModalBottomSheetState(initialDetent: SheetDetent = FullyExpanded): ModalBottomSheetState =
    rememberFtwModalBottomSheetState(initialDetent = initialDetent)

@Composable
fun rememberFtwHiddenModalBottomSheetState(initialDetent: SheetDetent = Hidden): ModalBottomSheetState =
    rememberFtwModalBottomSheetState(initialDetent = initialDetent)

// Extension for showing/hiding the sheet
suspend fun ModalBottomSheetState.toggleSheet(show: Boolean) {
    currentDetent = if (show) FullyExpanded else Hidden
}

// Composable to handle sheet visibility
@Composable
fun rememberSheetController(sheetState: ModalBottomSheetState = rememberFtwModalBottomSheetState()): SheetController {
    val scope = rememberCoroutineScope()

    return remember(sheetState) {
        SheetController(
            state = sheetState,
            scope = scope
        )
    }
}

class SheetController(
    val state: ModalBottomSheetState,
    private val scope: CoroutineScope,
    private val onSheetOpened: () -> Unit = {}, // Add callback
    private val onSheetClosed: () -> Unit = {} // Add callback
) {
    private var _isVisible = mutableStateOf(false)
    val isVisible: Boolean get() = _isVisible.value

    fun show() {
        scope.launch {
            state.toggleSheet(true)
            _isVisible.value = true
            onSheetOpened() // Invoke callback
        }
    }

    fun hide() {
        scope.launch {
            state.toggleSheet(false)
            _isVisible.value = false
            onSheetClosed() // Invoke callback
        }
    }

}