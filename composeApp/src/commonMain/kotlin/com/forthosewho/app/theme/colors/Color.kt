package com.forthosewho.app.theme.colors

import androidx.compose.ui.graphics.Color

val AthensGray = Color(0xFFF9F9FB)
val Beige = Color(0xFFF9F7F7)
val Beige12 = Color(0xFFF9F7F7)
val Beige50 = Color(0x80F9F7F7)
val BeigeAlto = Color(0xFFF0F0F0)
val Blue = Color(0xFF0029FF)
val Blue25 = Color(0x400029FF)
val Black = Color(0xFF000000)
val Black50 = Color(0x80000000)
val Citron12 = Color(0x979BB11F)
val Cyan = Color(0xFFF5FAFF)
val DeepCyan = Color(0xFFD9EBFF)
val Grey = Color(0xFFCBCDD8)
val GreySuperLight = Color(0xFFE0E2E5)
val GreyLight = Color(0xFF979BB1)
val Grey50 = Color(0x80979BB1)
val Lavender = Color(0xFFE0E5FF)
val Red = Color(0xFFF9325D)
val White = Color(0xFFFFFFFFF)
val WhiteTransparency = Color(0x80FFFFFF)

internal val Blue20 = Color(0xFF003544)
internal val Blue30 = Color(0xFF004D61)
internal val Blue80 = Color(0xFF5DD5FC)
internal val Blue90 = Color(0xFFB8EAFF)
internal val DarkGreenGray20 = Color(0xFF2F312E)
internal val DarkGreenGray95 = Color(0xFFF0F1EC)
internal val DarkPurpleGray10 = Color(0xFF201A1B)
internal val DarkPurpleGray90 = Color(0xFFECDFE0)
internal val GreenGray90 = Color(0xFFDDE5DB)
internal val Orange20 = Color(0xFF5B1A00)
internal val Orange30 = Color(0xFF812800)
internal val Orange80 = Color(0xFFFFB59B)
internal val Orange90 = Color(0xFFFFDBCF)
internal val Purple20 = Color(0xFF560A5D)
internal val Purple30 = Color(0xFF702776)
internal val Purple80 = Color(0xFFFFA9FE)
internal val Purple90 = Color(0xFFFFD6FA)
internal val PurpleGray30 = Color(0xFF4D444C)
internal val PurpleGray60 = Color(0xFF998D96)
internal val PurpleGray80 = Color(0xFFD0C3CC)
internal val Red10 = Color(0xFF410002)
internal val Red20 = Color(0xFF690005)
internal val Red30 = Color(0xFF93000A)
internal val Red40 = Color(0xFFBA1A1A)
internal val Red80 = Color(0xFFFFB4AB)
internal val Red90 = Color(0xFFFFDAD6)
internal val Teal10 = Color(0xFF001F26)
internal val Teal40 = Color(0xFF3A656F)
internal val Teal90 = Color(0xFFBEEAF6)
