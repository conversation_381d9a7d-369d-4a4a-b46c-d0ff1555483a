package com.forthosewho.app.theme.textfields

import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.DeepCyan
import com.forthosewho.app.theme.colors.Red

@Composable
fun OtpTextField(
    modifier: Modifier = Modifier,
    otpText: String,
    otpCount: Int = 4,
    isFalse: Boolean = false,
    onOtpTextChange: (String, Boolean) -> Unit,
    onFocusChange: (Boolean) -> Unit,
) {
    LaunchedEffect(Unit) {
        if (otpText.length > otpCount) {
            throw IllegalArgumentException("Must not have more than: $otpCount characters")
        }
    }

    BasicTextField(
        modifier = modifier.onFocusChanged { focusState ->
            onFocusChange(focusState.isFocused)
        },
        value = TextFieldValue(otpText, selection = TextRange(otpText.length)),
        onValueChange = {
            if (it.text.length <= otpCount) {
                onOtpTextChange.invoke(it.text, it.text.length == otpCount)
            }
        },
        interactionSource = MutableInteractionSource(),
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword),
        decorationBox = {
            Row(horizontalArrangement = Arrangement.Center) {
                repeat(otpCount) { index ->
                    CharView(
                        index = index,
                        text = otpText,
                        isFalse = isFalse
                    )
                    Spacer(modifier = Modifier.width(25.dp))
                }
            }
        }
    )
}

@Composable
private fun CharView(
    index: Int,
    text: String,
    isFalse: Boolean = false
) {
    val isFocused = text.length == index
    val isFilled = text.length >= index
    val char = when {
        index == text.length -> " "
        index > text.length -> ""
        else -> {
            text[index].toString()
        }
    }
    Text(
        modifier = Modifier
            .width(63.dp)
            .height(72.dp)
            .border(
                width = 1.dp,
                color = getColor(isFocused = isFocused, isFilled = isFilled, isFalse = isFalse),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(top = 20.dp, bottom = 10.dp),
        text = char,
        style = MaterialTheme.typography.bodyLarge.copy(fontSize = 32.sp),
        color = getColor(isFocused = isFocused, isFilled = isFilled, isFalse = isFalse),
        textAlign = TextAlign.Center
    )
}

fun getColor(isFocused: Boolean, isFilled: Boolean = false, isFalse: Boolean = false): Color {
    return when {
        isFalse -> Red
        isFocused -> DeepCyan
        isFilled -> Blue
        else -> DeepCyan
    }
}
