package com.forthosewho.app.theme.icon

import androidx.compose.material.icons.Icons
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val Icons.Stories: ImageVector
    get() {
        if (_stories != null) {
            return _stories!!
        }
        _stories = Builder(
            name = "Stories", defaultWidth = 64.0.dp, defaultHeight = 63.0.dp,
            viewportWidth = 64.0f, viewportHeight = 63.0f
        ).apply {
            path(
                fill = SolidColor(Color(0xFF979BB1)), stroke = null, strokeLineWidth = 0.0f,
                strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                pathFillType = NonZero
            ) {
                moveTo(27.5415f, 24.25f)
                curveTo(26.8853f, 24.25f, 26.4165f, 24.7656f, 26.4165f, 25.375f)
                verticalLineTo(39.625f)
                curveTo(26.4165f, 40.0469f, 26.3228f, 40.4219f, 26.1821f, 40.75f)
                horizontalLineTo(40.2915f)
                curveTo(40.9009f, 40.75f, 41.4165f, 40.2812f, 41.4165f, 39.625f)
                verticalLineTo(25.375f)
                curveTo(41.4165f, 24.7656f, 40.9009f, 24.25f, 40.2915f, 24.25f)
                horizontalLineTo(27.5415f)
                close()
                moveTo(23.0415f, 43.0f)
                curveTo(21.1665f, 43.0f, 19.6665f, 41.5f, 19.6665f, 39.625f)
                verticalLineTo(25.75f)
                curveTo(19.6665f, 25.1406f, 20.1353f, 24.625f, 20.7915f, 24.625f)
                curveTo(21.4009f, 24.625f, 21.9165f, 25.1406f, 21.9165f, 25.75f)
                verticalLineTo(39.625f)
                curveTo(21.9165f, 40.2812f, 22.3853f, 40.75f, 23.0415f, 40.75f)
                curveTo(23.6509f, 40.75f, 24.1665f, 40.2812f, 24.1665f, 39.625f)
                verticalLineTo(25.375f)
                curveTo(24.1665f, 23.5469f, 25.6665f, 22.0f, 27.5415f, 22.0f)
                horizontalLineTo(40.2915f)
                curveTo(42.1196f, 22.0f, 43.6665f, 23.5469f, 43.6665f, 25.375f)
                verticalLineTo(39.625f)
                curveTo(43.6665f, 41.5f, 42.1196f, 43.0f, 40.2915f, 43.0f)
                horizontalLineTo(23.0415f)
                close()
                moveTo(27.9165f, 26.875f)
                curveTo(27.9165f, 26.2656f, 28.3853f, 25.75f, 29.0415f, 25.75f)
                horizontalLineTo(33.5415f)
                curveTo(34.1509f, 25.75f, 34.6665f, 26.2656f, 34.6665f, 26.875f)
                verticalLineTo(30.625f)
                curveTo(34.6665f, 31.2812f, 34.1509f, 31.75f, 33.5415f, 31.75f)
                horizontalLineTo(29.0415f)
                curveTo(28.3853f, 31.75f, 27.9165f, 31.2812f, 27.9165f, 30.625f)
                verticalLineTo(26.875f)
                close()
                moveTo(37.2915f, 25.75f)
                horizontalLineTo(38.7915f)
                curveTo(39.4009f, 25.75f, 39.9165f, 26.2656f, 39.9165f, 26.875f)
                curveTo(39.9165f, 27.5312f, 39.4009f, 28.0f, 38.7915f, 28.0f)
                horizontalLineTo(37.2915f)
                curveTo(36.6353f, 28.0f, 36.1665f, 27.5312f, 36.1665f, 26.875f)
                curveTo(36.1665f, 26.2656f, 36.6353f, 25.75f, 37.2915f, 25.75f)
                close()
                moveTo(37.2915f, 29.5f)
                horizontalLineTo(38.7915f)
                curveTo(39.4009f, 29.5f, 39.9165f, 30.0156f, 39.9165f, 30.625f)
                curveTo(39.9165f, 31.2812f, 39.4009f, 31.75f, 38.7915f, 31.75f)
                horizontalLineTo(37.2915f)
                curveTo(36.6353f, 31.75f, 36.1665f, 31.2812f, 36.1665f, 30.625f)
                curveTo(36.1665f, 30.0156f, 36.6353f, 29.5f, 37.2915f, 29.5f)
                close()
                moveTo(29.0415f, 33.25f)
                horizontalLineTo(38.7915f)
                curveTo(39.4009f, 33.25f, 39.9165f, 33.7656f, 39.9165f, 34.375f)
                curveTo(39.9165f, 35.0312f, 39.4009f, 35.5f, 38.7915f, 35.5f)
                horizontalLineTo(29.0415f)
                curveTo(28.3853f, 35.5f, 27.9165f, 35.0312f, 27.9165f, 34.375f)
                curveTo(27.9165f, 33.7656f, 28.3853f, 33.25f, 29.0415f, 33.25f)
                close()
                moveTo(29.0415f, 37.0f)
                horizontalLineTo(38.7915f)
                curveTo(39.4009f, 37.0f, 39.9165f, 37.5156f, 39.9165f, 38.125f)
                curveTo(39.9165f, 38.7812f, 39.4009f, 39.25f, 38.7915f, 39.25f)
                horizontalLineTo(29.0415f)
                curveTo(28.3853f, 39.25f, 27.9165f, 38.7812f, 27.9165f, 38.125f)
                curveTo(27.9165f, 37.5156f, 28.3853f, 37.0f, 29.0415f, 37.0f)
                close()
            }
        }
            .build()
        return _stories!!
    }

private var _stories: ImageVector? = null
