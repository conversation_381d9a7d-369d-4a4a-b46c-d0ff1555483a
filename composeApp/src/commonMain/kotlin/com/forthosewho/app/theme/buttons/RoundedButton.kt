package com.forthosewho.app.theme.buttons

import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.White

@Composable
fun RoundedButton(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    colors: IconButtonColors = IconButtonColors(
        containerColor = Beige,
        contentColor = Black,
        disabledContainerColor = White,
        disabledContentColor = White,
    ),
    onClick: () -> Unit,
    content: @Composable () -> Unit,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val interactions = remember { mutableStateListOf<Interaction>() }

    LaunchedEffect(interactionSource) {
        interactionSource.interactions.collect { interaction ->
            when (interaction) {
                is PressInteraction.Press -> interactions.add(interaction)
                is PressInteraction.Release -> interactions.remove(interaction.press)
                is PressInteraction.Cancel -> interactions.remove(interaction.press)
            }
        }
    }
    IconButton(
        modifier = modifier,
        enabled = enabled,
        interactionSource = interactionSource,
        colors = colors,
        onClick = { onClick() }) {
        content()
    }
}
