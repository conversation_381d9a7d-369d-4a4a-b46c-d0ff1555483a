package com.forthosewho.app.theme.cards

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.ForThoseWhoTheme
import com.forthosewho.app.theme.icon.Profile
import com.forthosewho.app.theme.tags.Tag
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.coil3.CoilImage
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
fun SourceItem(
    modifier: Modifier = Modifier,
    iconModifier: Modifier = Modifier,
    title: String,
    imageUrl: String,
    thumbnailName: String,
    thumbnailUrl: String,
    onClick: () -> Unit,
) {
    Column(
        modifier = modifier.border(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline,
            shape = MaterialTheme.shapes.large,
        ).clickable { onClick() },
        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.Center,
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            SourceItemImage(
                modifier = iconModifier.size(90.dp),
                topicImageUrl = imageUrl,
            )
            Column(
                modifier = modifier.padding(start = 16.dp, end = 8.dp),
                verticalArrangement = Arrangement.Center,
            ) {
                Text(
                    text = title,
                    modifier = modifier,
                    style = MaterialTheme.typography.displaySmall,
                    maxLines = 3,
                    minLines = 1,
                    textAlign = TextAlign.Left,
                    softWrap = true,
                    overflow = TextOverflow.Ellipsis
                )
                Tag(
                    modifier = modifier.padding(top = 6.dp),
                    imageUrl = thumbnailUrl,
                    tagText = thumbnailName
                )
            }
        }
    }
}

@Composable
private fun SourceItemImage(
    topicImageUrl: String,
    modifier: Modifier = Modifier
) {
    if (topicImageUrl.isEmpty()) {
        Icon(
            modifier = modifier.background(MaterialTheme.colorScheme.surface),
            imageVector = Icons.Profile,
            contentDescription = null,
        )
    } else {
        CoilImage(
            modifier = modifier.clip(MaterialTheme.shapes.small),
            imageModel = { topicImageUrl }, // loading a network image or local resource using an URL.
            imageOptions = ImageOptions(
                contentDescription = null,
                contentScale = ContentScale.Crop,
                alignment = Alignment.Center,
            )
        )
    }
}

@Preview
@Composable
private fun SourceItemPreview() {
    ForThoseWhoTheme {
        Surface {
            SourceItem(
                thumbnailName = "Compose",
                title = "Compose",
                imageUrl = "",
                onClick = { },
                thumbnailUrl = ""
            )
        }
    }
}

@Preview
@Composable
private fun SourceItemLongNamePreview() {
    ForThoseWhoTheme {
        Surface {
            SourceItem(
                thumbnailName = "Compose",
                title = "Compose",
                imageUrl = "",
                onClick = { },
                thumbnailUrl = ""
            )
        }
    }
}

@Preview
@Composable
private fun SourceItemLongDescriptionPreview() {
    ForThoseWhoTheme {
        Surface {
            SourceItem(
                thumbnailName = "Compose",
                title = "Compose",
                imageUrl = "",
                onClick = { },
                thumbnailUrl = ""
            )
        }
    }
}

@Preview
@Composable
private fun SourceItemWithEmptyDescriptionPreview() {
    ForThoseWhoTheme {
        Surface {
            SourceItem(
                thumbnailName = "Compose",
                title = "Compose",
                imageUrl = "",
                onClick = { },
                thumbnailUrl = ""
            )
        }
    }
}

@Preview
@Composable
private fun SourceItemSelectedPreview() {
    ForThoseWhoTheme {
        Surface {
            SourceItem(
                thumbnailName = "Compose",
                title = "Compose",
                imageUrl = "",
                onClick = { },
                thumbnailUrl = ""
            )
        }
    }
}