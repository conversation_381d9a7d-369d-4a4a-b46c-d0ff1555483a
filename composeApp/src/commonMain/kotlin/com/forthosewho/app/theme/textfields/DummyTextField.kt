package com.forthosewho.app.theme.textfields

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp

@Composable
fun DummyTextField(
    modifier: Modifier,
    backgroundColor: Color,
    dummyText: String,
    dummyTextColor: Color,
    trailingIcon: ImageVector,
    trailingIconTint: Color
) {
    Row(
        modifier = modifier.clip(RoundedCornerShape(40.dp)).background(backgroundColor),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = dummyText,
            color = dummyTextColor,
            modifier = Modifier.wrapContentHeight().padding(horizontal = 16.dp, vertical = 20.dp).weight(1F),
        )
        Icon(
            modifier = Modifier.padding(horizontal = 16.dp),
            imageVector = trailingIcon,
            tint = trailingIconTint,
            contentDescription = null
        )
    }
}

