package com.forthosewho.app.theme.type

import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import forthosewho_app.composeapp.generated.resources.Acumin_BdPro
import forthosewho_app.composeapp.generated.resources.Acumin_RPro
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.SourceSerifPro_Regular
import org.jetbrains.compose.resources.Font

object Type {

    val typography: Typography
        @Composable get() {
            return CustomTypography()
        }

    @Composable
    private fun CustomTypography(): Typography {
        val acuminRegular = FontFamily(Font(Res.font.Acumin_RPro))
        val acuminBold = FontFamily(Font(Res.font.Acumin_BdPro))
        val sourceSerifRegular = FontFamily(Font(Res.font.SourceSerifPro_Regular))

        val typo = Typography(
            displayLarge = TextStyle(
                fontFamily = sourceSerifRegular,
                fontWeight = FontWeight.W600,
                fontSize = 32.sp,
                lineHeight = 36.sp,
                textAlign = TextAlign.Left,
            ),
            // TextStyle label used on card title
            displayMedium = TextStyle(
                fontFamily = sourceSerifRegular,
                fontWeight = FontWeight.W600,
                fontSize = 22.sp,
                lineHeight = 27.sp,
                textAlign = TextAlign.Left,
            ),
            // TextStyle label used on bottoms sheet
            displaySmall = TextStyle(
                fontFamily = sourceSerifRegular,
                fontWeight = FontWeight.W600,
                fontSize = 16.sp,
                lineHeight = 19.sp,
                textAlign = TextAlign.Left,
            ),
            headlineLarge = TextStyle(
                fontFamily = sourceSerifRegular,
                fontWeight = FontWeight.Bold,
                fontSize = 32.sp,
            ),
            headlineMedium = TextStyle(
                fontFamily = acuminBold,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                lineHeight = 16.sp,
                textAlign = TextAlign.Center,
            ),
            headlineSmall = TextStyle(
                fontFamily = sourceSerifRegular,
                fontWeight = FontWeight.W600,
                fontSize = 16.sp,
                lineHeight = 19.sp,
                textAlign = TextAlign.Left,
            ),
            //Main toolbar title style
            titleLarge = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.W600,
                letterSpacing = (-0.5).sp,
                fontSize = 24.sp,
                lineHeight = 29.sp,
                textAlign = TextAlign.Left,
            ),
            //Secondary toolbar title style
            titleMedium = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.W600,
                letterSpacing = (-0.5).sp,
                fontSize = 20.sp,
                lineHeight = 24.sp,
                textAlign = TextAlign.Left,
            ),
            // TextStyle label used on tag
            titleSmall = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.W600,
                letterSpacing = (-0.5).sp,
                fontSize = 18.sp,
                lineHeight = 20.sp,
                textAlign = TextAlign.Left,
            ),
            // TextStyle used on card description
            bodyLarge = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.W500,
                fontSize = 16.sp,
                lineHeight = 22.4.sp,
                textAlign = TextAlign.Left,
            ),

            bodyMedium = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.Normal,
                fontSize = 17.sp,
                lineHeight = 17.sp,
                letterSpacing = (-0.5).sp,
                textAlign = TextAlign.Left,
            ),
            //TextStyle used in clue chips
            bodySmall = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.Normal,
                fontSize = 16.sp,
                lineHeight = 19.sp,
                letterSpacing = (-0.5).sp,
            ),
            // TextStyle label used on login info screens
            labelLarge = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.Normal,
                fontSize = 17.sp,
                lineHeight = 17.sp,
                letterSpacing = (-0.5).sp,
                textAlign = TextAlign.Left,
            ),
            // TextStyle label used on card footer
            labelMedium = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.W500,
                fontSize = 14.sp,
                lineHeight = 17.sp,
                textAlign = TextAlign.Right,
            ),
            // TextStyle label used on tag
            labelSmall = TextStyle(
                fontFamily = acuminRegular,
                fontWeight = FontWeight.W600,
                fontSize = 12.sp,
                lineHeight = 12.sp,
            )
        )
        return typo
    }
}
