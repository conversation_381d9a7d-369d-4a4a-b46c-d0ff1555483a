package com.forthosewho.app.theme.toolbars

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.ForThoseWhoTheme
import com.forthosewho.app.theme.buttons.RoundedButton
import org.jetbrains.compose.ui.tooling.preview.Preview

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StoriesTopAppBar(
    title: String,
    titleStyle: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.titleLarge,
    navigationIcon: ImageVector? = null,
    navigationIconContentDescription: String? = null,
    actionIcon: ImageVector? = null,
    actionIconContentDescription: String? = null,
    modifier: Modifier = Modifier,
    colors: TopAppBarColors = TopAppBarDefaults.topAppBarColors(),
    onNavigationClick: () -> Unit = {},
    onActionClick: () -> Unit = {},
) {
    TopAppBar(
        modifier = modifier.padding(start = 4.dp, end = 16.dp),
        title = { Text(text = title, style = titleStyle) },
        navigationIcon = {
            if (navigationIcon == null) return@TopAppBar
            IconButton(onClick = onNavigationClick) {
                Icon(
                    imageVector = navigationIcon,
                    contentDescription = navigationIconContentDescription,
                    tint = MaterialTheme.colorScheme.onSurface,
                )
            }
        },
        actions = {
            if (actionIcon == null) return@TopAppBar
            RoundedButton(
                modifier = Modifier.size(36.dp),
                onClick = onActionClick
            ) {
                Icon(
                    imageVector = actionIcon,
                    contentDescription = actionIconContentDescription,
                    tint = MaterialTheme.colorScheme.onSurface,
                )
            }
        },
        colors = colors,
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmallStoriesTopAppBar(
    title: String,
    titleStyle: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.titleMedium,
    actionIcon: ImageVector? = null,
    actionIconContentDescription: String,
    colors: TopAppBarColors = TopAppBarDefaults.topAppBarColors(),
    modifier: Modifier = Modifier,
    onActionClick: () -> Unit = {},
) {
    Row(
        modifier = modifier,
        verticalAlignment = androidx.compose.ui.Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        Text(
            text = title,
            style = titleStyle,
            modifier = Modifier.weight(1f).padding(end = 16.dp),
            color = colors.titleContentColor
        )
        if (actionIcon == null) return@Row
        RoundedButton(
            modifier = Modifier.height(36.dp).width(36.dp),
            onClick = onActionClick
        ) {
            Icon(
                imageVector = actionIcon,
                contentDescription = actionIconContentDescription,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StoriesWithSubtitleTopAppBar(
    modifier: Modifier = Modifier,
    title: String,
    titleStyle: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.titleLarge,
    subtitle: String,
    subtitleStyle: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.labelMedium,
    colors: TopAppBarColors = TopAppBarDefaults.topAppBarColors(),
    leftIconButton: ImageVector? = null,
    rightIconButton: ImageVector? = null,
    leftIconButtonContentDescription: String? = null,
    rightIconButtonContentDescription: String? = null,
    onLeftIconClicked: () -> Unit = {},
    onRightIconClicked: () -> Unit = {},
) {
    StoriesWithSubtitleTopAppBarContent(
        modifier = modifier,
        leftIconButton = {
            leftIconButton?.let {
                RoundedButton(
                    modifier = Modifier.size(36.dp),
                    onClick = onLeftIconClicked
                ) {
                    Icon(
                        imageVector = leftIconButton,
                        contentDescription = leftIconButtonContentDescription,
                        tint = MaterialTheme.colorScheme.onSurface,
                    )
                }
            }
        },
        textContent = {
            Text(
                text = title,
                style = titleStyle,
                textAlign = TextAlign.Left,
                color = colors.titleContentColor,
            )
            Text(
                text = subtitle,
                style = subtitleStyle,
                color = colors.titleContentColor,
                textAlign = TextAlign.Left,
                modifier = Modifier.padding(top = 8.dp),
                minLines = 1,
                maxLines = 3,
            )
        },
        rightIconButton = {
            rightIconButton?.let {
                RoundedButton(
                    modifier = Modifier.size(36.dp),
                    onClick = onRightIconClicked
                ) {
                    Icon(
                        imageVector = rightIconButton,
                        contentDescription = rightIconButtonContentDescription,
                        tint = MaterialTheme.colorScheme.onSurface,
                    )
                }
            }
        }
    )
}

@Composable
private fun StoriesWithSubtitleTopAppBarContent(
    modifier: Modifier = Modifier,
    leftIconButton: @Composable () -> Unit = {},
    textContent: @Composable () -> Unit = {},
    rightIconButton: @Composable () -> Unit = {},
) {
    Row(modifier = modifier.padding(top = 20.dp, bottom = 20.dp)) {
        leftIconButton()
        Spacer(modifier = Modifier.width(20.dp))
        Column(modifier = Modifier.weight(1f)) {
            textContent()
        }
        Spacer(modifier = Modifier.width(20.dp))
        rightIconButton()
        Spacer(modifier = Modifier.width(20.dp))
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Preview()
@Composable
private fun StoriesTopAppBarPreview() {
    ForThoseWhoTheme {
        StoriesTopAppBar(
            title = "Stories for all your clues",
            navigationIcon = Icons.Rounded.KeyboardArrowDown,
            navigationIconContentDescription = "Navigation icon",
            actionIcon = Icons.Rounded.KeyboardArrowDown,
            actionIconContentDescription = "Action icon",
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview()
@Composable
private fun SmallStoriesTopAppBarPreview() {
    ForThoseWhoTheme {
        SmallStoriesTopAppBar(
            title = "Stories for all your clues",
            actionIcon = Icons.Rounded.Close,
            actionIconContentDescription = "Action icon",
        )
    }
}
