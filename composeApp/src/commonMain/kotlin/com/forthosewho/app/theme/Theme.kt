package com.forthosewho.app.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MaterialTheme.colorScheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.colors.BackgroundTheme
import com.forthosewho.app.theme.colors.LightColorScheme
import com.forthosewho.app.theme.colors.LocalBackgroundTheme
import com.forthosewho.app.theme.shapes.ExtendedShapes
import com.forthosewho.app.theme.shapes.LocalAppShapes
import com.forthosewho.app.theme.spacing.ExtendedSpacing
import com.forthosewho.app.theme.spacing.LocalAppSpacing
import com.forthosewho.app.theme.type.Type

internal val LocalThemeIsDark = compositionLocalOf { mutableStateOf(true) }

@Composable
internal fun ForThoseWhoTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {
    val defaultBackgroundTheme = BackgroundTheme(
        color = colorScheme.surface,
        tonalElevation = 2.dp,
    )

    val isDarkState = remember { mutableStateOf(darkTheme) }

    CompositionLocalProvider(
        LocalThemeIsDark provides isDarkState,
        LocalBackgroundTheme provides defaultBackgroundTheme,
        LocalAppShapes provides ExtendedShapes(),
        LocalAppSpacing provides ExtendedSpacing(),
        LocalTextStyle provides Type.typography.bodyMedium,
    ) {
        val isDark by isDarkState
        SystemAppearance(!isDark)
        MaterialTheme(
            typography = Type.typography,
            colorScheme = getColorScheme(),
            content = { Surface(content = content) }
        )
    }
}

@Composable
internal expect fun SystemAppearance(isDark: Boolean)

@Composable
fun getColorScheme(darkTheme: Boolean = isSystemInDarkTheme()): ColorScheme {
//    return if (darkTheme) {
//        DarkColorScheme
//    } else {
//        LightColorScheme
//    }
    return LightColorScheme
}
