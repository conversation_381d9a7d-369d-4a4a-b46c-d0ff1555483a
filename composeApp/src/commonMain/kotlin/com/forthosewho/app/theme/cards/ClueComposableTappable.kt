package com.forthosewho.app.theme.cards

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.ForThoseWhoTheme
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.buttons.RoundedButton
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.icon.FtwIcons.Check
import com.mikepenz.markdown.m3.Markdown
import com.mikepenz.markdown.m3.markdownTypography
import org.intellij.markdown.flavours.commonmark.CommonMarkFlavourDescriptor
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
fun ClueComposableTappable(
    text: String,
    modifier: Modifier,
    containerColor: Color = White,
    contentColor: Color = Black,
    imageVector: ImageVector = Check,
    iconButtonColors: IconButtonColors = IconButtonColors(
        containerColor = Blue,
        contentColor = White,
        disabledContainerColor = GreyLight,
        disabledContentColor = White
    ),
    iconTint: Color = White,
    enabled: Boolean = true,
    onClick: () -> Unit = {}
) {
    var enabled by remember { mutableStateOf(enabled) }

    Card(
        modifier = modifier.clickable {
            enabled = !enabled
            onClick()
        },
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(),
        colors = CardDefaults.cardColors(
            containerColor = containerColor,
            contentColor = contentColor
        ),
    ) {
        Row(
            modifier = Modifier,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Markdown(
                content = text,
                modifier = Modifier
                    .weight(0.8F)
                    .wrapContentHeight()
                    .padding(start = 16.dp, top = 15.dp, bottom = 16.dp),
                typography = markdownTypography(paragraph = MaterialTheme.typography.bodySmall),
                flavour = CommonMarkFlavourDescriptor()
            )
            Box(
                modifier = Modifier.weight(0.2F).padding(horizontal = 16.dp),
                contentAlignment = Alignment.CenterEnd
            ) {
                RoundedButton(
                    modifier = Modifier.size(24.dp),
                    enabled = enabled,
                    onClick = { onClick() },
                    colors = iconButtonColors
                ) {
                    Icon(
                        modifier = Modifier.width(14.dp).wrapContentHeight(),
                        imageVector = imageVector,
                        contentDescription = null,
                        tint = iconButtonColors.contentColor
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun ClueComposableTappablePreview() {
    ForThoseWhoTheme {
        Surface {
            Box(modifier = Modifier.background(Beige)) {
                ClueComposableTappable(
                    text = "You are a foodie and you love travelling a you are a foodie and you love travelling, You are a foodie and you love travelling, You are a foodie and you love travelling.",
                    modifier = Modifier
                )
            }
        }
    }
}
