package com.forthosewho.app.theme.transitions

import androidx.compose.animation.core.Easing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.tween

fun tweenFtwDefault(
    durationMillis: Int = 150,
    easing: Easing = LinearOutSlowInEasing
): TweenSpec<Float> {
    return tween(
        durationMillis = durationMillis,
        easing = easing
    )
}