package com.forthosewho.app.theme.tags

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.ForThoseWhoTheme
import com.forthosewho.app.theme.colors.Grey
import com.forthosewho.app.theme.colors.GreyLight
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.coil3.CoilImage
import org.jetbrains.compose.ui.tooling.preview.Preview

private const val UNFOLLOWED_TOPIC_TAG_CONTAINER_ALPHA = 0.5f

@Composable
fun Tag(
    modifier: Modifier = Modifier,
    tagText: String,
    imageUrl: String,
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(30.dp),
        colors = CardColors(
            containerColor = MaterialTheme.colorScheme.secondary,
            contentColor = MaterialTheme.colorScheme.onSecondary,
            disabledContainerColor = MaterialTheme.colorScheme.secondary.copy(
                alpha = UNFOLLOWED_TOPIC_TAG_CONTAINER_ALPHA
            ),
            disabledContentColor = MaterialTheme.colorScheme.onSecondary.copy(
                alpha = UNFOLLOWED_TOPIC_TAG_CONTAINER_ALPHA
            )
        ),
        elevation = CardDefaults.cardElevation(),
    ) {
        Row(
            modifier = Modifier.padding(2.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            CoilImage(
                modifier = Modifier.size(22.dp).border(
                    width = 1.dp,
                    color = Grey,
                    shape = CircleShape
                ).clip(CircleShape),
                imageModel = { imageUrl },
                imageOptions = ImageOptions(
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.Center
                )
            )
            Text(
                text = tagText,
                style = MaterialTheme.typography.labelSmall,
                color = GreyLight,
                modifier = Modifier.padding(
                    start = 8.dp,
                    end = 8.dp
                ),
            )
        }
    }

}

@Preview
@Composable
fun TagPreview() {
    ForThoseWhoTheme {
        Tag(
            tagText = "Topic",
            imageUrl = "https://production.forthosewho.com/images/sources/b070d13d-17a2-4532-a14f-8c88ca180f7b.png",
        )
    }
}
