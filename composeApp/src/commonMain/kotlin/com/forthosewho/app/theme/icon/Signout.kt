package com.forthosewho.app.theme.icon

import androidx.compose.material.icons.Icons
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val Icons.Signout: ImageVector
    get() {
        if (_signout != null) {
            return _signout!!
        }
        _signout = Builder(name = "Signout", defaultWidth = 17.0.dp, defaultHeight = 14.0.dp,
                viewportWidth = 17.0f, viewportHeight = 14.0f).apply {
            path(fill = SolidColor(Color(0xFF000000)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(6.0f, 13.25f)
                curveTo(6.0f, 13.6875f, 5.6563f, 14.0f, 5.25f, 14.0f)
                horizontalLineTo(3.0f)
                curveTo(1.3125f, 14.0f, 0.0f, 12.6875f, 0.0f, 11.0f)
                verticalLineTo(3.0f)
                curveTo(0.0f, 1.3438f, 1.3125f, 0.0f, 3.0f, 0.0f)
                horizontalLineTo(5.25f)
                curveTo(5.6563f, 0.0f, 6.0f, 0.3438f, 6.0f, 0.75f)
                curveTo(6.0f, 1.1875f, 5.6563f, 1.5f, 5.25f, 1.5f)
                horizontalLineTo(3.0f)
                curveTo(2.1563f, 1.5f, 1.5f, 2.1875f, 1.5f, 3.0f)
                verticalLineTo(11.0f)
                curveTo(1.5f, 11.8438f, 2.1563f, 12.5f, 3.0f, 12.5f)
                horizontalLineTo(5.25f)
                curveTo(5.6563f, 12.5f, 6.0f, 12.8438f, 6.0f, 13.25f)
                close()
                moveTo(15.7812f, 6.5f)
                lineTo(11.8125f, 2.25f)
                curveTo(11.5312f, 1.9375f, 11.0625f, 1.9375f, 10.75f, 2.2188f)
                curveTo(10.4375f, 2.5f, 10.4375f, 2.9688f, 10.7188f, 3.2813f)
                lineTo(13.5f, 6.25f)
                horizontalLineTo(5.7188f)
                curveTo(5.3125f, 6.25f, 5.0f, 6.5938f, 5.0f, 7.0f)
                curveTo(5.0f, 7.4375f, 5.3125f, 7.75f, 5.7188f, 7.75f)
                horizontalLineTo(13.4688f)
                lineTo(10.6562f, 10.75f)
                curveTo(10.375f, 11.0625f, 10.375f, 11.5312f, 10.6875f, 11.8125f)
                curveTo(10.875f, 11.9375f, 11.0625f, 12.0f, 11.2188f, 12.0f)
                curveTo(11.4062f, 12.0f, 11.5938f, 11.9375f, 11.75f, 11.7812f)
                lineTo(15.7188f, 7.5313f)
                curveTo(16.0625f, 7.25f, 16.0625f, 6.7813f, 15.7812f, 6.5f)
                close()
            }
        }
        .build()
        return _signout!!
    }

private var _signout: ImageVector? = null
