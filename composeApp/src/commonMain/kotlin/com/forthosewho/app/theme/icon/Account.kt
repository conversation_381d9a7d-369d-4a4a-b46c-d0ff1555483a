package com.forthosewho.app.theme.icon

import androidx.compose.material.icons.Icons
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val Icons.Account: ImageVector
    get() {
        if (_account != null) {
            return _account!!
        }
        _account = Builder(
            name = "Account", defaultWidth = 64.0.dp, defaultHeight = 63.0.dp,
            viewportWidth = 64.0f, viewportHeight = 63.0f
        ).apply {
            path(
                fill = SolidColor(Color(0xFF979BB1)), stroke = null, strokeLineWidth = 0.0f,
                strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                pathFillType = NonZero
            ) {
                moveTo(32.333f, 25.75f)
                curveTo(34.583f, 25.75f, 36.458f, 27.625f, 36.458f, 29.875f)
                curveTo(36.458f, 32.1719f, 34.583f, 34.0f, 32.333f, 34.0f)
                curveTo(30.0361f, 34.0f, 28.208f, 32.1719f, 28.208f, 29.875f)
                curveTo(28.208f, 27.625f, 30.0361f, 25.75f, 32.333f, 25.75f)
                close()
                moveTo(32.333f, 31.75f)
                curveTo(33.3643f, 31.75f, 34.208f, 30.9531f, 34.208f, 29.875f)
                curveTo(34.208f, 28.8438f, 33.3643f, 28.0f, 32.333f, 28.0f)
                curveTo(31.2549f, 28.0f, 30.458f, 28.8438f, 30.458f, 29.875f)
                curveTo(30.458f, 30.9531f, 31.2549f, 31.75f, 32.333f, 31.75f)
                close()
                moveTo(32.333f, 20.5f)
                curveTo(38.9424f, 20.5f, 44.333f, 25.8906f, 44.333f, 32.5f)
                curveTo(44.333f, 39.1562f, 38.9424f, 44.5f, 32.333f, 44.5f)
                curveTo(25.6768f, 44.5f, 20.333f, 39.1562f, 20.333f, 32.5f)
                curveTo(20.333f, 25.8906f, 25.6768f, 20.5f, 32.333f, 20.5f)
                close()
                moveTo(32.333f, 42.25f)
                curveTo(34.4893f, 42.25f, 36.5049f, 41.5469f, 38.1455f, 40.3281f)
                curveTo(37.3486f, 38.7812f, 35.7549f, 37.75f, 33.9736f, 37.75f)
                horizontalLineTo(30.6455f)
                curveTo(28.8643f, 37.75f, 27.2705f, 38.7344f, 26.4736f, 40.3281f)
                curveTo(28.1143f, 41.5469f, 30.1299f, 42.25f, 32.333f, 42.25f)
                close()
                moveTo(39.833f, 38.7344f)
                curveTo(41.2393f, 37.0469f, 42.083f, 34.8906f, 42.083f, 32.5f)
                curveTo(42.083f, 27.1562f, 37.6768f, 22.75f, 32.333f, 22.75f)
                curveTo(26.9424f, 22.75f, 22.583f, 27.1562f, 22.583f, 32.5f)
                curveTo(22.583f, 34.8906f, 23.3799f, 37.0469f, 24.7861f, 38.7344f)
                curveTo(26.0518f, 36.7656f, 28.208f, 35.5f, 30.6455f, 35.5f)
                horizontalLineTo(33.9736f)
                curveTo(36.4111f, 35.5f, 38.5674f, 36.7656f, 39.833f, 38.7344f)
                close()
            }
        }
            .build()
        return _account!!
    }

private var _account: ImageVector? = null
