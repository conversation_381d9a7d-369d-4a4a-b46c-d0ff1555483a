package com.forthosewho.app.theme.cards

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mikepenz.markdown.m3.Markdown
import com.mikepenz.markdown.m3.markdownTypography
import org.intellij.markdown.flavours.commonmark.CommonMarkFlavourDescriptor

@Composable
fun ClueComposable(
    text: String,
    modifier: Modifier,
    colors: CardColors = CardDefaults.cardColors(
        containerColor = MaterialTheme.colorScheme.surfaceVariant,
        contentColor = MaterialTheme.colorScheme.onSurface,
    ),
) {
    Card(
        shape = CardDefaults.shape,
        colors = colors,
        elevation = CardDefaults.cardElevation(),
    ) {
        Markdown(
            content = text,
            modifier = modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 16.dp, vertical = 12.dp),
            typography = markdownTypography(paragraph = MaterialTheme.typography.bodySmall),
            flavour = CommonMarkFlavourDescriptor()
        )
    }
}
