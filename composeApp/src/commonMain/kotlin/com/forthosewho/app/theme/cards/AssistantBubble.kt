package com.forthosewho.app.theme.cards

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.forthosewho.app.theme.colors.Lavender
import com.forthosewho.app.theme.indicators.DotsFlashing
import com.mikepenz.markdown.m3.Markdown
import com.mikepenz.markdown.m3.markdownTypography
import org.intellij.markdown.flavours.commonmark.CommonMarkFlavourDescriptor

@Composable
fun AssistantBubble(
    text: String,
    isUser: Boolean = true,
    isLoading: Boolean = false,
) {
    Box(modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(bottom = 16.dp)) {
        Card(
            modifier = Modifier.wrapContentSize().align(getAlignment(isUser)),
            shape = RoundedCornerShape(20.dp),
            colors = getCardColors(isUser),
            elevation = CardDefaults.cardElevation(),
        ) {
            if (isLoading && !isUser) {
                DotsFlashing(modifier = Modifier.padding(16.dp))
            } else {
                Markdown(
                    modifier = Modifier.padding(16.dp),
                    content = text,
                    typography = markdownTypography(
                        paragraph = MaterialTheme.typography.bodySmall.copy(textAlign = getTextAlign(isUser))
                    ),
                    flavour = CommonMarkFlavourDescriptor()
                )
            }
        }
    }
}

@Composable
fun getCardColors(isUser: Boolean): CardColors = if (isUser) {
    CardDefaults.cardColors(
        containerColor = Lavender,
        contentColor = MaterialTheme.colorScheme.onSurface,
    )
} else {
    CardDefaults.cardColors(
        containerColor = MaterialTheme.colorScheme.surfaceVariant,
        contentColor = MaterialTheme.colorScheme.onSurface,
    )
}

private fun getTextAlign(isUser: Boolean): TextAlign = if (isUser) {
    TextAlign.End
} else {
    TextAlign.Start
}

private fun getAlignment(isUser: Boolean): Alignment = if (isUser) {
    Alignment.TopEnd
} else {
    Alignment.TopStart
}

