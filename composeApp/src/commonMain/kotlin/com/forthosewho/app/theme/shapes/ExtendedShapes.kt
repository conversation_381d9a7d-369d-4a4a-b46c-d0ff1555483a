package com.forthosewho.app.theme.shapes

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.dp

/**
 * Data class representing the extended shapes used in the application.
 *
 * @param cardShape The custom shape for cards.
 * @param bottomSheetShape The custom shape for bottom sheets.
 */
@Immutable
data class ExtendedShapes(
    val cardShape: RoundedCornerShape = RoundedCornerShape(16.dp),
    val buttonShape: RoundedCornerShape = RoundedCornerShape(8.dp),
    val chipShape: RoundedCornerShape = RoundedCornerShape(14.dp),
    val textFieldShape: RoundedCornerShape = RoundedCornerShape(8.dp),
    val variantShape: RoundedCornerShape = RoundedCornerShape(8.dp),
    val imageShape: RoundedCornerShape = RoundedCornerShape(8.dp),
    val pillShape: RoundedCornerShape = RoundedCornerShape(30.dp),
    val statusShape: RoundedCornerShape = RoundedCornerShape(6.dp),
    val bottomSheetShape: Shape = RectangleShape,
)

/**
 * A [CompositionLocal] to store the custom shapes, making them accessible throughout the Compose
 * hierarchy.
 */
val LocalAppShapes = compositionLocalOf { ExtendedShapes() }
