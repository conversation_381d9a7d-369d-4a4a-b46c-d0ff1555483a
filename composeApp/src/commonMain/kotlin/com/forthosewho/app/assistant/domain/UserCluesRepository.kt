package com.forthosewho.app.assistant.domain

import com.forthosewho.app.account.data.entity.ClueRaw
import com.forthosewho.app.assistant.ui.model.CluesContainer
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.github.michaelbull.result.Result

interface UserCluesRepository {
    suspend fun clues(
        cluesToBeAdded: List<ClueRaw>,
        cluesToBeRemoved: List<ClueRaw>,
        token: String,
        userId: String
    ): Result<CluesContainer, ErrorResponse>
}
