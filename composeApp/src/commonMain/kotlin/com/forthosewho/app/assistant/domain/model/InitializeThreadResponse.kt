package com.forthosewho.app.assistant.domain.model

import com.forthosewho.app.platform.utils.empty

data class InitializeThreadResponse(
    val threadId: String,
    val messageItems: List<MessageItem>
) {

    companion object {

        val emptyInitializeThreadResponse = InitializeThreadResponse(
            threadId = String.empty(),
            messageItems = listOf()
        )
    }

    fun toDomain() = InitializeThreadResponse(
        threadId = threadId,
        messageItems = messageItems.map { it.toDomain() }
    )
}
