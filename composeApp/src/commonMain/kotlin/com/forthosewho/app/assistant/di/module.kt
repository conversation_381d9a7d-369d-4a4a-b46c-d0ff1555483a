package com.forthosewho.app.assistant.di

import com.forthosewho.app.assistant.data.remote.RemoteInitializeThreadRepository
import com.forthosewho.app.assistant.data.remote.RemoteMessageRepository
import com.forthosewho.app.assistant.data.remote.RemoteRetrieveCluesRepository
import com.forthosewho.app.assistant.data.remote.UserCluesRepository
import com.forthosewho.app.assistant.domain.usecase.InitializeThreadUseCase
import com.forthosewho.app.assistant.domain.usecase.RetrieveCluesUseCase
import com.forthosewho.app.assistant.domain.usecase.SendMessageUseCase
import com.forthosewho.app.assistant.domain.usecase.UserCluesUseCase
import com.forthosewho.app.assistant.ui.viewmodel.AssistantViewModel
import com.forthosewho.app.network.di.CURRENT_BASE_URL
import com.forthosewho.app.network.di.DEFAULT_CLIENT
import org.koin.core.module.dsl.viewModel
import org.koin.core.qualifier.named
import org.koin.dsl.module

val assistantModule = module {
    viewModel {
        AssistantViewModel(
            initializeThreadUseCase = get(),
            sendMessageUseCase = get(),
            meUseCase = get(),
            userCluesUseCase = get(),
            retrieveCluesUseCase = get(),
            analyticsUseCase = get()
        )
    }
    single { SendMessageUseCase(remoteMessageRepository = get()) }
    single { InitializeThreadUseCase(remoteInitializeThreadRepository = get()) }
    single { UserCluesUseCase(userCluesRepository = get()) }
    single { RetrieveCluesUseCase(remoteRetrieveCluesRepository = get()) }

    single { RemoteRetrieveCluesRepository(client = get(named(DEFAULT_CLIENT)), baseUrl = get(named(CURRENT_BASE_URL))) }
    single { RemoteMessageRepository(client = get(named(DEFAULT_CLIENT)), baseUrl = get(named(CURRENT_BASE_URL))) }
    single { RemoteInitializeThreadRepository(client = get(named(DEFAULT_CLIENT)), baseUrl = get(named(CURRENT_BASE_URL))) }
    single { UserCluesRepository(client = get(named(DEFAULT_CLIENT)), baseUrl = get(named(CURRENT_BASE_URL))) }
}