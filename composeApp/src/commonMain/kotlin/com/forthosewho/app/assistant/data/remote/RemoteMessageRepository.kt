package com.forthosewho.app.assistant.data.remote

import com.forthosewho.app.assistant.data.entity.MessageItemRaw
import com.forthosewho.app.assistant.data.entity.MessageItemRaw.Companion.emptyMessage
import com.forthosewho.app.assistant.domain.MessageRepository
import com.forthosewho.app.assistant.domain.model.MessageItem
import com.forthosewho.app.network.createUrl
import com.forthosewho.app.network.getResults
import io.ktor.client.HttpClient
import io.ktor.client.request.setBody
import io.ktor.http.HttpMethod

private const val SEND_MESSAGE_API = "/v0/assistant/send-message"
private const val THREAD_ID = "threadId"
private const val MESSAGE = "message"
private const val CONTINUE_DISCUSSION = "continueDiscussion"

class RemoteMessageRepository(private val client: HttpClient, private val baseUrl: String) : MessageRepository {

    override suspend fun sendMessage(
        threadId: String, token: String, message: String, continueDiscussion: Boolean
    ) = client.getResults<MessageItemRaw, MessageItem>(
        requestBlock = {
            createUrl(host = baseUrl, path = SEND_MESSAGE_API) {
                setBody(
                    mapOf(
                        THREAD_ID to threadId,
                        MESSAGE to message,
                        CONTINUE_DISCUSSION to continueDiscussion.toString()
                    )
                )
                method = HttpMethod.Post
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { messageItemRaw -> messageItemRaw?.toDomain() ?: emptyMessage },
    )
}
