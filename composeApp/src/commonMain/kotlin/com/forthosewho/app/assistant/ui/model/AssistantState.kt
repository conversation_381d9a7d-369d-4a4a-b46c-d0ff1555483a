package com.forthosewho.app.assistant.ui.model

import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.assistant.domain.model.Message
import com.forthosewho.app.platform.utils.empty

class AssistantState {

    data class State(
        val screenState: ScreenState = ScreenState.StateInitial,
        val isOnboarding: Boolean = false,
        val category: InitializeThreadCategory = InitializeThreadCategory.CHANGE_CLUES,
        val clueId: String? = null,
        val threadId: String = String.empty(),
        val messages: List<AssistantMessage> = listOf(),
        val container: CluesContainer = CluesContainer()
    )

    sealed class ScreenState {
        data object StateInitial : ScreenState()
        data object StateDiscussion : ScreenState()
        data object StateCluesOnboardingVerifyMode : ScreenState()
        data object StateCluesVerifyMode : ScreenState()
        data object StateCluesBlockingState : ScreenState()
        data object StateCluesNoUpdate : ScreenState()
        data object StateDiscussionEnd : ScreenState()
    }
}

data class AssistantMessage(
    val id: String,
    val text: String,
    val isUser: Boolean,
    val isLoading: Boolean,
) {

    companion object {

        fun createLoadingAssistantMessage(): AssistantMessage = AssistantMessage(
            id = "assistant",
            text = String.empty(),
            isUser = false,
            isLoading = true,
        )

        fun createUserAnswerMessage(answer: String): AssistantMessage = AssistantMessage(
            id = "user",
            text = answer,
            isUser = true,
            isLoading = false,
        )

        fun createAssistantMessage(id: String, assistantMessage: String): AssistantMessage = AssistantMessage(
            id = id,
            text = assistantMessage,
            isUser = false,
            isLoading = false,
        )
    }
}

data class CluesContainer(
    val cluesToBeAdded: List<Clue> = emptyList(),
    val cluesToBeRemoved: List<Clue> = emptyList(),
    val cluesToRemain: List<Clue> = emptyList(),
) {
    companion object {
        val emptyCluesContainer = CluesContainer()

        fun createCluesContainer(message: Message) = CluesContainer(
            cluesToBeAdded = message.cluesToBeAdded,
            cluesToBeRemoved = message.cluesToBeRemoved,
            cluesToRemain = message.cluesToRemain,
        )
    }

    fun hasCluesToBeAdded() = cluesToBeAdded.isNotEmpty()
    fun hasCluesToBeRemoved() = cluesToBeRemoved.isNotEmpty()
}

enum class InitializeThreadCategory(val category: String) {
    ONBOARDING("onboarding"),
    CHANGE_CLUES("change_clues"),
    EDIT_CLUE("edit_clue"),
}
