package com.forthosewho.app.assistant.ui

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.displayCutoutPadding
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.core.ModalBottomSheet
import com.composables.core.ModalBottomSheetState
import com.composables.core.ModalSheetProperties
import com.composables.core.Scrim
import com.composables.core.Sheet
import com.composables.core.SheetDetent.Companion.FullyExpanded
import com.composables.core.SheetDetent.Companion.Hidden
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.assistant.ui.model.AssistantMessage
import com.forthosewho.app.assistant.ui.model.AssistantState
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.assistant.ui.viewmodel.AssistantViewModel
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.theme.buttons.MainClickToActionButton
import com.forthosewho.app.theme.buttons.RoundedButton
import com.forthosewho.app.theme.cards.AssistantBubble
import com.forthosewho.app.theme.cards.ClueComposable
import com.forthosewho.app.theme.cards.ClueComposableTappable
import com.forthosewho.app.theme.colors.AthensGray
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.colors.Beige12
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Blue25
import com.forthosewho.app.theme.colors.Grey50
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.GreySuperLight
import com.forthosewho.app.theme.colors.Red
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.colors.WhiteTransparency
import com.forthosewho.app.theme.icon.FtwIcons.Check
import com.forthosewho.app.theme.icon.FtwIcons.Minus
import com.forthosewho.app.theme.icon.FtwIcons.Send
import com.forthosewho.app.theme.toolbars.SmallStoriesTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.assistant_verification_dialog_complete_profile
import forthosewho_app.composeapp.generated.resources.close_chat
import forthosewho_app.composeapp.generated.resources.clues_we_will_add
import forthosewho_app.composeapp.generated.resources.clues_we_will_remove
import forthosewho_app.composeapp.generated.resources.discard_conversation
import forthosewho_app.composeapp.generated.resources.here_you_told_us_about_you
import forthosewho_app.composeapp.generated.resources.keep_chatting
import forthosewho_app.composeapp.generated.resources.no_updates
import forthosewho_app.composeapp.generated.resources.no_updates_subtitle
import forthosewho_app.composeapp.generated.resources.share_anything
import forthosewho_app.composeapp.generated.resources.start_typing_conversation
import forthosewho_app.composeapp.generated.resources.update_profile_and_close
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun AssistantBottomSheetDialog(
    category: InitializeThreadCategory,
    itemId: String? = null,
    viewModel: AssistantViewModel = koinViewModel<AssistantViewModel>(),
    modalSheetState: ModalBottomSheetState,
    onSheetClosed: () -> Unit = {}, // Add callback
    onDismissRequest: () -> Unit,
) {
    val state by viewModel.state.collectAsState()

    // Monitor sheet state changes
    LaunchedEffect(modalSheetState.currentDetent) {
        when (modalSheetState.currentDetent) {
            FullyExpanded -> viewModel.initializeDiscussion(category = category, itemId = itemId)
            Hidden -> onSheetClosed()
        }
    }

    ModalBottomSheet(
        state = modalSheetState,
        onDismiss = onDismissRequest,
        properties = ModalSheetProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        ),
    ) {
        Scrim(scrimColor = Color.Black.copy(0.3f), enter = fadeIn(), exit = fadeOut())
        Sheet(
            modifier = Modifier
                .padding(top = 12.dp)
                .displayCutoutPadding()
                .statusBarsPadding()
                .padding(WindowInsets.navigationBars.only(WindowInsetsSides.Horizontal).asPaddingValues())
                .shadow(4.dp, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .background(White)
                .widthIn(max = 640.dp)
                .fillMaxWidth()
                .imePadding(),
            enabled = false
        ) {
            when (state.screenState) {
                is AssistantState.ScreenState.StateInitial -> {
                    Content(
                        toolbarContent = {
                            if (state.isOnboarding) {
                                Toolbar(
                                    title = stringResource(Res.string.share_anything),
                                    onActionClick = { viewModel.retrieveClues() }
                                )
                            } else {
                                Toolbar(
                                    title = stringResource(Res.string.share_anything),
                                    onActionClick = { viewModel.retrieveClues() }
                                )
                            }
                        },
                        content = {
                            Column(
                                modifier = Modifier.fillMaxWidth().fillMaxHeight(),
                                verticalArrangement = Arrangement.Bottom,
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                AssistantMessageList(
                                    modifier = Modifier.weight(1f).fillMaxWidth(),
                                    assistantMessageList = state.messages,
                                )
                                AssistantTextField(
                                    modifier = Modifier.wrapContentHeight(),
                                    onSendEvent = { viewModel.sendEvent(it) }
                                )
                            }
                        },
                    )
                }

                is AssistantState.ScreenState.StateDiscussion, AssistantState.ScreenState.StateCluesBlockingState -> {
                    Content(
                        toolbarContent = {
                            if (state.isOnboarding) {
                                Toolbar(
                                    title = stringResource(Res.string.share_anything),
                                    onActionClick = { viewModel.retrieveClues() }
                                )
                            } else {
                                Toolbar(
                                    title = stringResource(Res.string.share_anything),
                                    onActionClick = { viewModel.retrieveClues() }
                                )
                            }
                        },
                        content = {
                            Column(
                                modifier = Modifier.fillMaxWidth().fillMaxHeight(),
                                verticalArrangement = Arrangement.Bottom,
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                AssistantMessageList(
                                    modifier = Modifier.weight(1f).fillMaxWidth(),
                                    assistantMessageList = state.messages,
                                )
                                AssistantTextField(
                                    modifier = Modifier.wrapContentHeight(),
                                    onSendEvent = { viewModel.sendEvent(it) }
                                )
                            }
                        },
                        loadingContent = {
                            if (state.screenState is AssistantState.ScreenState.StateCluesBlockingState) {
                                Box(
                                    modifier = Modifier.fillMaxWidth().fillMaxHeight()
                                        .background(WhiteTransparency).clickable {},
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(48.dp),
                                        strokeWidth = 5.dp,
                                        color = Blue
                                    )
                                }
                            }
                        }
                    )
                }

                is AssistantState.ScreenState.StateCluesOnboardingVerifyMode -> {
                    Content(
                        toolbarContent = {
                            Toolbar(
                                title = stringResource(Res.string.here_you_told_us_about_you),
                                onActionClick = { viewModel.exitAssistant() }
                            )
                        },
                        content = {
                            CluesOnboardingComposable(
                                state = state,
                                onVerifyCluesClicked = { viewModel.verifyClues() },
                            )
                        },
                    )
                }

                is AssistantState.ScreenState.StateCluesVerifyMode -> {
                    Content(
                        toolbarContent = {
                            Toolbar(
                                title = stringResource(Res.string.here_you_told_us_about_you),
                                onActionClick = { viewModel.backToDiscussion() }
                            )
                        },
                        content = {
                            CluesVerificationComposable(
                                state = state,
                                onClueToBeAddedClicked = { viewModel.onClueToBeAddedClicked(it) },
                                onClueToBeRemovedClicked = { viewModel.onClueToBeRemovedClicked(it) },
                                onVerifyCluesClicked = { viewModel.verifyClues() },
                                onDiscardConversationClicked = {
                                    onDismissRequest()
                                    viewModel.exitAssistant()
                                },
                            )
                        },
                    )
                }

                is AssistantState.ScreenState.StateCluesNoUpdate -> {
                    Content(
                        toolbarContent = {
                            Toolbar(
                                title = stringResource(Res.string.here_you_told_us_about_you),
                                hideClose = true
                            )
                        },
                        content = {
                            CluesNoUpdatesComposable(
                                state = state,
                                onCloseChat = {
                                    onDismissRequest()
                                    viewModel.exitAssistant()
                                },
                                onKeepChatting = {
                                    viewModel.backToDiscussion()
                                }
                            )
                        },
                    )
                }

                is AssistantState.ScreenState.StateDiscussionEnd -> {
                    onDismissRequest()
                    viewModel.exitAssistant()
                }
            }
        }
    }
}

@Composable
fun CluesVerificationComposable(
    state: AssistantState.State,
    onClueToBeAddedClicked: (Clue) -> Unit,
    onClueToBeRemovedClicked: (Clue) -> Unit,
    onVerifyCluesClicked: () -> Unit,
    onDiscardConversationClicked: () -> Unit,
) {
    var isVerifyLoading by remember { mutableStateOf(false) }
    var isDiscardLoading by remember { mutableStateOf(false) }
    var isVerifyEnabled by remember { mutableStateOf(true) }
    var isDiscardEnabled by remember { mutableStateOf(true) }

    Column(
        modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 20.dp),
        verticalArrangement = Arrangement.Bottom,
        horizontalAlignment = Alignment.Start
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        if (state.container.hasCluesToBeAdded()) {
            Column(modifier = Modifier.padding(vertical = 8.dp)) {
                Text(
                    text = stringResource(Res.string.clues_we_will_add),
                    style = MaterialTheme.typography.bodySmall,
                    color = Black,
                )
                Spacer(modifier = Modifier.height(4.dp))
                CluesToComposable(
                    modifier = Modifier.fillMaxWidth(),
                    clues = state.container.cluesToBeAdded,
                    onClueClicked = { onClueToBeAddedClicked(it) }
                )
            }
        }
        if (state.container.hasCluesToBeRemoved()) {
            Column(modifier = Modifier.padding(vertical = 8.dp)) {
                Text(
                    text = stringResource(Res.string.clues_we_will_remove),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Black,
                )
                Spacer(modifier = Modifier.height(4.dp))
                CluesToComposable(
                    modifier = Modifier.fillMaxWidth(),
                    clues = state.container.cluesToBeRemoved,
                    onClueClicked = { onClueToBeRemovedClicked(it) }
                )
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        MainClickToActionButton(
            buttonText = stringResource(Res.string.update_profile_and_close),
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            colors = ButtonColors(
                containerColor = Blue,
                contentColor = White,
                disabledContainerColor = Blue25,
                disabledContentColor = GreyLight,
            ),
            isLoading = isVerifyLoading,
            enabled = isVerifyEnabled,
            buttonTextColor = White,
            onButtonClick = {
                isVerifyLoading = true
                isVerifyEnabled = false
                isDiscardEnabled = false
                onVerifyCluesClicked()
            }
        )
        Spacer(modifier = Modifier.height(16.dp))
        MainClickToActionButton(
            buttonText = stringResource(Res.string.discard_conversation),
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            colors = ButtonColors(
                containerColor = Beige,
                contentColor = Red,
                disabledContainerColor = Beige,
                disabledContentColor = GreyLight,
            ),
            buttonTextColor = Red,
            isLoading = isDiscardLoading,
            enabled = isDiscardEnabled,
            onButtonClick = {
                isDiscardLoading = true
                isVerifyEnabled = false
                isDiscardEnabled = false
                onDiscardConversationClicked()
            }
        )
        Spacer(modifier = Modifier.height(48.dp))
    }
}

@Composable
fun CluesNoUpdatesComposable(
    state: AssistantState.State,
    onCloseChat: () -> Unit,
    onKeepChatting: () -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 20.dp),
        verticalArrangement = Arrangement.Bottom,
        horizontalAlignment = Alignment.Start
    ) {
        Spacer(modifier = Modifier.height(8.dp))
        Card {
            Text(
                text = stringResource(Res.string.no_updates),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.displayMedium,
                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(top = 16.dp),
            )
            Text(
                text = stringResource(Res.string.no_updates_subtitle),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.W500, lineHeight = 25.sp),
                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(16.dp),
            )
        }
        Spacer(modifier = Modifier.height(16.dp))
        if (!state.isOnboarding) {
            MainClickToActionButton(
                buttonText = stringResource(Res.string.close_chat),
                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                colors = ButtonColors(
                    containerColor = Blue,
                    contentColor = White,
                    disabledContainerColor = GreySuperLight,
                    disabledContentColor = GreyLight,
                ),
                buttonTextColor = White,
                onButtonClick = { onCloseChat() }
            )
            Spacer(modifier = Modifier.height(16.dp))
        }
        MainClickToActionButton(
            buttonText = stringResource(Res.string.keep_chatting),
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            colors = ButtonColors(
                containerColor = Beige,
                contentColor = Blue,
                disabledContainerColor = Beige,
                disabledContentColor = GreyLight,
            ),
            buttonTextColor = Blue,
            onButtonClick = { onKeepChatting() }
        )
        Spacer(modifier = Modifier.height(48.dp))
    }
}


@Composable
fun CluesOnboardingComposable(
    state: AssistantState.State,
    onVerifyCluesClicked: () -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 20.dp),
        verticalArrangement = Arrangement.Bottom,
        horizontalAlignment = Alignment.Start
    ) {
        Spacer(modifier = Modifier.height(8.dp))
        if (state.isOnboarding) {
            Column(modifier = Modifier.padding(vertical = 8.dp)) {
                Box(
                    modifier = Modifier.fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp))
                        .border(width = 8.dp, color = Beige12)
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .background(Beige12),
                ) {
                    LazyColumn(
                        modifier = Modifier.padding(8.dp),
                        verticalArrangement = Arrangement.spacedBy(space = 8.dp),
                        horizontalAlignment = Alignment.Start,
                    ) {
                        state.container.cluesToBeAdded.forEach { clue ->
                            item(key = clue.clue) {
                                ClueComposable(
                                    modifier = Modifier,
                                    text = clue.clue,
                                    colors = CardColors(
                                        containerColor = White,
                                        contentColor = Black,
                                        disabledContainerColor = White,
                                        disabledContentColor = Grey50,
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        MainClickToActionButton(
            buttonText = stringResource(Res.string.assistant_verification_dialog_complete_profile),
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            colors = ButtonColors(
                containerColor = Blue,
                contentColor = White,
                disabledContainerColor = GreySuperLight,
                disabledContentColor = GreyLight,
            ),
            buttonTextColor = White,
            onButtonClick = { onVerifyCluesClicked() }
        )
        Spacer(modifier = Modifier.height(48.dp))
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun Toolbar(
    title: String = stringResource(Res.string.share_anything),
    hideClose: Boolean = false,
    onActionClick: () -> Unit = {},
) {
    if (hideClose) {
        SmallStoriesTopAppBar(
            title = title,
            titleStyle = MaterialTheme.typography.titleMedium,
            actionIconContentDescription = "Action icon",
            modifier = Modifier.padding(
                top = 20.dp,
                bottom = 8.dp,
                start = 20.dp,
                end = 20.dp
            ).fillMaxWidth().wrapContentHeight(),
        )
    } else {
        SmallStoriesTopAppBar(
            title = title,
            titleStyle = MaterialTheme.typography.titleMedium,
            actionIcon = Icons.Rounded.Close,
            actionIconContentDescription = "Action icon",
            modifier = Modifier.padding(
                top = 20.dp,
                bottom = 8.dp,
                start = 20.dp,
                end = 20.dp
            ).fillMaxWidth().wrapContentHeight(),
            onActionClick = onActionClick
        )
    }
}

@Composable
private fun Content(
    toolbarContent: @Composable () -> Unit = {},
    content: @Composable () -> Unit = {},
    loadingContent: @Composable () -> Unit = {},
) {
    Column(modifier = Modifier) {
        toolbarContent()
        Box(modifier = Modifier) {
            content()
            loadingContent()
        }
    }
}

@Composable
fun AssistantMessageList(
    modifier: Modifier,
    assistantMessageList: List<AssistantMessage>,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.End,
) {
    val listState = rememberLazyListState()

    LaunchedEffect(assistantMessageList.size) {
        if (assistantMessageList.isNotEmpty()) {
            listState.animateScrollToItem(assistantMessageList.size - 1)
        }
    }

    LazyColumn(
        reverseLayout = false,
        modifier = modifier.padding(horizontal = 16.dp),
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
        state = listState
    ) {
        items(count = assistantMessageList.size) { index ->
            assistantMessageList[index].run {
                AssistantBubble(
                    text = this.text,
                    isUser = this.isUser,
                    isLoading = this.isLoading,
                )
            }
        }
    }
}

@Composable
fun AssistantTextField(
    modifier: Modifier,
    onSendEvent: (String) -> Unit,
) {
    var answer by remember { mutableStateOf(String.empty()) }

    HorizontalDivider(thickness = 1.dp, color = Grey50)

    OutlinedTextField(
        enabled = true,
        colors = OutlinedTextFieldDefaults.colors(
            unfocusedTextColor = Black,
            unfocusedBorderColor = White,
            unfocusedLabelColor = White,
            focusedTextColor = Black,
            focusedBorderColor = White,
            focusedLabelColor = White,
        ),
        value = answer,
        onValueChange = {
            answer = it
        },
        placeholder = {
            Text(
                text = stringResource(Res.string.start_typing_conversation),
                style = MaterialTheme.typography.labelMedium.copy(fontWeight = FontWeight.W500),
                color = GreyLight,
                textAlign = TextAlign.Start
            )
        },
        singleLine = false,
        trailingIcon = {
            if (answer.isNotEmpty()) {
                RoundedTrailingIcon(
                    modifier = Modifier.height(36.dp).width(36.dp),
                    enabled = true,
                    onRoundedButtonClicked = {
                        onSendEvent(answer)
                        answer = String.empty()
                    },
                )
            } else {
                RoundedTrailingIcon(
                    modifier = Modifier.height(36.dp).width(36.dp),
                    enabled = false,
                )
            }

        },
        shape = RectangleShape,
        modifier = modifier.fillMaxWidth().height(100.dp).padding(end = 16.dp),
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Text,
            imeAction = ImeAction.Done
        ),
    )
}

@Composable
fun RoundedTrailingIcon(
    modifier: Modifier,
    enabled: Boolean,
    onRoundedButtonClicked: () -> Unit = {},
) {
    RoundedButton(
        modifier = modifier,
        enabled = enabled,
        onClick = { onRoundedButtonClicked() },
        colors = IconButtonColors(
            containerColor = Blue,
            contentColor = White,
            disabledContainerColor = GreyLight,
            disabledContentColor = GreyLight,
        )
    ) {
        Icon(
            imageVector = Send,
            contentDescription = null,
            tint = White,
        )
    }
}

@Composable
fun CluesToComposable(
    modifier: Modifier = Modifier,
    clues: List<Clue>,
    onClueClicked: (Clue) -> Unit,
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = 8.dp),
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .border(
                width = 8.dp,
                color = Beige12,
            )
            .fillMaxWidth()
            .wrapContentHeight()
            .background(Beige12),
    ) {
        LazyColumn(
            modifier = Modifier.padding(8.dp),
            verticalArrangement = verticalArrangement,
            horizontalAlignment = horizontalAlignment,
        ) {
            clues.forEach { clue ->
                item(key = clue.clue) {
                    ClueComposableTappable(
                        modifier = Modifier,
                        imageVector = getImageVector(clue),
                        containerColor = getContainerColor(clue),
                        contentColor = getContentColor(clue),
                        text = clue.clue,
                        enabled = clue.enabled,
                        onClick = { onClueClicked(clue) }
                    )
                }
            }
        }
    }
}

private fun getImageVector(clue: Clue): ImageVector {
    return if (clue.enabled) {
        Check
    } else {
        Minus
    }
}

private fun getContainerColor(clue: Clue): Color {
    return if (clue.enabled) {
        White
    } else {
        AthensGray
    }
}

private fun getContentColor(clue: Clue): Color {
    return if (clue.enabled) {
        Black
    } else {
        Grey50
    }
}
