package com.forthosewho.app.assistant.domain.usecase

import com.forthosewho.app.assistant.data.remote.RemoteInitializeThreadRepository
import com.forthosewho.app.assistant.domain.model.InitializeThreadResponse
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.github.michaelbull.result.Result
import com.forthosewho.app.login.domain.model.ErrorResponse

class InitializeThreadUseCase(
    private val remoteInitializeThreadRepository: RemoteInitializeThreadRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
) {
    suspend operator fun invoke(param: Params): Result<InitializeThreadResponse, ErrorResponse> {
        return remoteInitializeThreadRepository.initializeThread(
            category = param.category,
            itemId = param.itemId,
            userId = getStoredValueUseCase.getString(key = DataStoreValues.USER_ID),
            email = getStoredValueUseCase.getString(key = DataStoreValues.EMAIL),
            token = getStoredValueUseCase.getString(key = DataStoreValues.ACCESS_TOKEN),
        )
    }

    data class Params(
        val category: String,
        val itemId: String?,
    )
}
