package com.forthosewho.app.assistant.domain

import com.forthosewho.app.assistant.domain.model.MessageItem
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.github.michaelbull.result.Result

interface MessageRepository {
    suspend fun sendMessage(
        threadId: String,
        token: String,
        message: String,
        continueDiscussion: Boolean
    ): Result<MessageItem, ErrorResponse>
}
