package com.forthosewho.app.assistant.data.remote

import com.forthosewho.app.assistant.data.entity.InitializeThreadResponseRaw
import com.forthosewho.app.assistant.domain.InitializeThreadRepository
import com.forthosewho.app.assistant.domain.model.InitializeThreadResponse
import com.forthosewho.app.assistant.domain.model.InitializeThreadResponse.Companion.emptyInitializeThreadResponse
import com.forthosewho.app.network.createUrl

import com.forthosewho.app.network.getResults
import io.ktor.client.HttpClient
import io.ktor.client.request.setBody
import io.ktor.http.HttpMethod

private const val INITIALIZE_THREAD_API = "/v0/assistant/initialize-thread"
private const val CATEGORY = "category"
private const val USER_ID = "userId"
private const val CLUE_ID = "clueId"
private const val EMAIL = "user_email"

class RemoteInitializeThreadRepository(private val client: HttpClient, private val baseUrl: String) :
    InitializeThreadRepository {
    override suspend fun initializeThread(
        category: String,
        itemId: String?,
        email: String,
        token: String,
        userId: String
    ) = client.getResults<InitializeThreadResponseRaw, InitializeThreadResponse>(
        requestBlock = {
            createUrl(host = baseUrl, path = INITIALIZE_THREAD_API) {
                setBody(
                    mapOf(
                        CATEGORY to category,
                        USER_ID to userId,
                        CLUE_ID to itemId,
                        EMAIL to email
                    )
                )
                method = HttpMethod.Post
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { initializeThreadResponseRaw ->
            initializeThreadResponseRaw?.toDomain() ?: emptyInitializeThreadResponse
        },
    )
}
