package com.forthosewho.app.assistant.domain

import com.forthosewho.app.assistant.domain.model.InitializeThreadResponse
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.github.michaelbull.result.Result

interface InitializeThreadRepository {
    suspend fun initializeThread(
        category: String,
        itemId: String?,
        email: String,
        token: String,
        userId: String
    ): Result<InitializeThreadResponse,ErrorResponse>
}
