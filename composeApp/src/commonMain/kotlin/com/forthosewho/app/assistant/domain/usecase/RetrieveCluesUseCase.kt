package com.forthosewho.app.assistant.domain.usecase

import com.forthosewho.app.assistant.data.remote.RemoteRetrieveCluesRepository
import com.forthosewho.app.assistant.domain.model.MessageItem
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.github.michaelbull.result.Result

class RetrieveCluesUseCase(
    private val remoteRetrieveCluesRepository: RemoteRetrieveCluesRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
) {
    suspend operator fun invoke(param: Params): Result<MessageItem, ErrorResponse> {
        return remoteRetrieveCluesRepository.retrieveClues(
            category = param.category,
            userId = getStoredValueUseCase.getString(key = DataStoreValues.USER_ID),
            threadId = param.threadId,
        )
    }

    data class Params(
        val category: String,
        val threadId: String,
    )
}