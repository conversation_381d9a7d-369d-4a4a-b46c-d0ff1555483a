package com.forthosewho.app.assistant.data.remote

import com.forthosewho.app.account.data.entity.ClueRaw
import com.forthosewho.app.assistant.data.entity.RetrieveCluesResponseRaw
import com.forthosewho.app.assistant.domain.UserCluesRepository
import com.forthosewho.app.assistant.ui.model.CluesContainer
import com.forthosewho.app.assistant.ui.model.CluesContainer.Companion.emptyCluesContainer
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.network.createUrl
import com.forthosewho.app.network.getResults
import io.ktor.client.HttpClient
import io.ktor.client.request.setBody
import io.ktor.http.HttpMethod
import com.github.michaelbull.result.Result

private const val CLUES_API = "/v0/users/{USER_ID}/clues"
private const val CLUES_TO_BE_ADDED = "cluesToBeAdded"
private const val CLUES_TO_BE_REMOVED = "cluesToBeRemoved"

class UserCluesRepository(private val client: HttpClient, private val baseUrl: String) : UserCluesRepository {

    override suspend fun clues(
        cluesToBeAdded: List<ClueRaw>,
        cluesToBeRemoved: List<ClueRaw>,
        token: String,
        userId: String
    ): Result<CluesContainer, ErrorResponse> = client.getResults<RetrieveCluesResponseRaw, CluesContainer>(
        requestBlock = {
            createUrl(host = baseUrl, path = CLUES_API.replace("{USER_ID}", userId)) {
                setBody(
                    mapOf(
                        CLUES_TO_BE_ADDED to cluesToBeAdded,
                        CLUES_TO_BE_REMOVED to cluesToBeRemoved,
                    )
                )
                method = HttpMethod.Post
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { meRaw -> meRaw?.toDomain() ?: emptyCluesContainer },
    )
}
