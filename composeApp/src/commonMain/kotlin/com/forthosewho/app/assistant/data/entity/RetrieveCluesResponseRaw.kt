package com.forthosewho.app.assistant.data.entity

import com.forthosewho.app.account.data.entity.ClueRaw
import com.forthosewho.app.assistant.ui.model.CluesContainer
import kotlinx.serialization.Serializable

@Serializable
data class RetrieveCluesResponseRaw(
    private val clues: List<String> = emptyList(),
    private val cluesToBeAdded: List<ClueRaw> = emptyList(),
    private val cluesToBeRemoved: List<ClueRaw> = emptyList(),
) {
    fun toDomain(): CluesContainer = CluesContainer(
        cluesToBeAdded = cluesToBeAdded.map { it.toDomain() },
        cluesToBeRemoved = cluesToBeRemoved.map { it.toDomain() },
    )
}
