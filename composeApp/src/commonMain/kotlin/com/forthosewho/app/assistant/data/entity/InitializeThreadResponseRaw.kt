package com.forthosewho.app.assistant.data.entity

import com.forthosewho.app.assistant.domain.model.InitializeThreadResponse
import kotlinx.serialization.Serializable

@Serializable
data class InitializeThreadResponseRaw(
    private val threadId: String,
    private val messages: List<MessageItemRaw> = arrayListOf()
) {
    fun toDomain() = InitializeThreadResponse(
        threadId = threadId,
        messageItems = messages.map { it.toDomain() }
    )
}