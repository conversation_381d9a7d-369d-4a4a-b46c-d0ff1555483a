package com.forthosewho.app.assistant.domain.usecase

import com.forthosewho.app.account.data.entity.ClueRaw
import com.forthosewho.app.assistant.data.remote.UserCluesRepository
import com.forthosewho.app.assistant.ui.model.CluesContainer
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.github.michaelbull.result.Result

class UserCluesUseCase(
    private val userCluesRepository: UserCluesRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
) {
    suspend operator fun invoke(param: Params): Result<CluesContainer,ErrorResponse> {
        return userCluesRepository.clues(
            cluesToBeAdded = param.cluesContainer.cluesToBeAdded.filterNot { !it.enabled }.map(ClueRaw::from),
            cluesToBeRemoved = param.cluesContainer.cluesToBeRemoved.filterNot { !it.enabled }.map(ClueRaw::from),
            token = getToken(),
            userId = getUserId(),
        )
    }

    private suspend fun getUserId(): String = getStoredValueUseCase.getString(key = DataStoreValues.USER_ID)

    private suspend fun getToken(): String = getStoredValueUseCase.getString(key = DataStoreValues.ACCESS_TOKEN)

    data class Params(val cluesContainer: CluesContainer)
}