package com.forthosewho.app.assistant.domain.model

import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.platform.utils.empty

data class Message(
    val messageString: String,
    val cluesToBeAdded: List<Clue>,
    val cluesToBeRemoved: List<Clue>,
    val cluesToRemain: List<Clue>,
) {
    companion object {
        val emptyMessage = Message(
            messageString = String.empty(),
            cluesToBeAdded = emptyList(),
            cluesToBeRemoved = emptyList(),
            cluesToRemain = emptyList(),
        )
    }

    fun toDomain() = Message(
        messageString = messageString,
        cluesToBeAdded = cluesToBeAdded,
        cluesToBeRemoved = cluesToBeRemoved,
        cluesToRemain = cluesToRemain,
    )

    private fun hasCluesToBeAdded() = cluesToBeAdded.isNotEmpty()
    private fun hasCluesToBeRemoved() = cluesToBeRemoved.isNotEmpty()
    fun hasCluesToChange() = hasCluesToBeAdded() || hasCluesToBeRemoved()

    internal fun clueToBeAddedCount() = cluesToBeAdded.count()
    internal fun clueToBeRemovedCount() = cluesToBeRemoved.count()
    internal fun clueToBeRemainCount() = cluesToRemain.count()
}