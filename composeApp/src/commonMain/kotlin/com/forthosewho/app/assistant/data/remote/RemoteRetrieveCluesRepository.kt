package com.forthosewho.app.assistant.data.remote

import com.forthosewho.app.assistant.data.entity.MessageItemRaw
import com.forthosewho.app.assistant.data.entity.MessageItemRaw.Companion.emptyMessage
import com.forthosewho.app.assistant.domain.RetrieveCluesRepository
import com.forthosewho.app.assistant.domain.model.MessageItem
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.network.createUrl
import com.forthosewho.app.network.getResults
import com.github.michaelbull.result.Result
import io.ktor.client.HttpClient
import io.ktor.client.request.setBody
import io.ktor.http.HttpMethod

private const val RETRIEVE_CLUES_API = "/v0/assistant/retrieve-clues"
private const val CATEGORY = "category"
private const val USER_ID = "userId"
private const val THREAD_ID = "threadId"

class RemoteRetrieveCluesRepository(private val client: HttpClient, private val baseUrl: String) : RetrieveCluesRepository {
    override suspend fun retrieveClues(
        category: String,
        userId: String,
        threadId: String,
    ): Result<MessageItem, ErrorResponse> = client.getResults<MessageItemRaw, MessageItem>(
        requestBlock = {
            createUrl(host = baseUrl, path = RETRIEVE_CLUES_API) {
                setBody(mapOf(CATEGORY to category, USER_ID to userId, THREAD_ID to threadId))
                method = HttpMethod.Post
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { responseRaw -> responseRaw?.toDomain() ?: emptyMessage },
    )
}

