package com.forthosewho.app.assistant.data.entity

import com.forthosewho.app.account.data.entity.ClueRaw
import com.forthosewho.app.assistant.domain.model.Message
import com.forthosewho.app.platform.utils.empty
import kotlinx.serialization.Serializable

@Serializable
data class MessageRaw(
    private val next_message: String? = String.empty(),
    private val cluesToBeAdded: List<ClueRaw>? = emptyList(),
    private val cluesToBeRemoved: List<ClueRaw>? = emptyList(),
    private val cluesToRemain: List<ClueRaw>? = emptyList(),
) {

    companion object {

        val emptyMessage = Message(
            messageString = String.empty(),
            cluesToBeAdded = emptyList(),
            cluesToBeRemoved = emptyList(),
            cluesToRemain = emptyList(),
        )
    }

    fun toDomain() = Message(
        messageString = next_message ?: String.empty(),
        cluesToBeAdded = cluesToBeAdded?.map { it.toDomain() } ?: emptyList(),
        cluesToBeRemoved = cluesToBeRemoved?.map { it.toDomain() } ?: emptyList(),
        cluesToRemain = cluesToRemain?.map { it.toDomain() } ?: emptyList(),
    )
}
