package com.forthosewho.app.assistant.data.entity

import com.forthosewho.app.assistant.domain.model.Message
import com.forthosewho.app.assistant.domain.model.MessageItem
import com.forthosewho.app.platform.utils.empty
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

@Serializable
data class MessageItemRaw(
    private val role: String,
    private val message: String,
    private val id: String
) {

    companion object {
        val emptyMessage = MessageItem(
            role = String.empty(),
            message = Message.emptyMessage,
            id = String.empty()
        )
    }

    fun toDomain() = MessageItem(
        role = role,
        message = deserialize(message).toDomain(),
        id = id
    )

    private fun deserialize(jsonMessage: String): MessageRaw {
        val json = Json {
            ignoreUnknownKeys = true
            isLenient = true
        }
        return json.decodeFromString(MessageRaw.serializer(), jsonMessage)
    }
}
