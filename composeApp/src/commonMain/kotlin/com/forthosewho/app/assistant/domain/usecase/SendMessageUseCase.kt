package com.forthosewho.app.assistant.domain.usecase

import com.forthosewho.app.assistant.data.entity.MessageRaw
import com.forthosewho.app.assistant.data.remote.RemoteMessageRepository
import com.forthosewho.app.assistant.domain.model.MessageItem
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.github.michaelbull.result.Result
import com.forthosewho.app.login.domain.model.ErrorResponse
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.encodeToJsonElement

class SendMessageUseCase(
    private val remoteMessageRepository: RemoteMessageRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
) {
    suspend operator fun invoke(param: Params): Result<MessageItem, ErrorResponse> {
        return remoteMessageRepository.sendMessage(
            threadId = param.threadId,
            token = getToken(),
            message = encoding(MessageRaw(next_message = param.message)).toString(),
            continueDiscussion = param.continueDiscussion
        )
    }

    private suspend fun getToken(): String = getStoredValueUseCase.getString(key = DataStoreValues.ACCESS_TOKEN)

    data class Params(
        val threadId: String,
        val message: String?,
        val continueDiscussion: Boolean,
    )

    private fun encoding(jsonMessage: MessageRaw): JsonElement {
        return Json.encodeToJsonElement(jsonMessage)
    }
}