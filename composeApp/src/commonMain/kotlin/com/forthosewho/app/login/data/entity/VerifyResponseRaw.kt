package com.forthosewho.app.login.data.entity

import com.forthosewho.app.login.domain.model.Verify
import kotlinx.serialization.Serializable

@Serializable
data class VerifyResponseRaw(
    private val data: VerifyDataRaw? = null,
) {
    fun toVerify() = Verify(
        username = data?.username.toString(),
        password = data?.password.toString(),
        metadata = data?.metadata.toString()
    )
}

@Serializable
data class VerifyDataRaw(
    internal val username: String? = null,
    internal val password: String? = null,
    internal val metadata: String? = null,
)