package com.forthosewho.app.login.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.login.analytics.UserOnboardingSignupEvents
import com.forthosewho.app.login.domain.model.Me
import com.forthosewho.app.login.domain.model.ValidationResult
import com.forthosewho.app.login.domain.usecase.AuthLoginUseCase
import com.forthosewho.app.login.domain.usecase.MeUseCase
import com.forthosewho.app.login.domain.usecase.ValidateEmailUseCase
import com.forthosewho.app.login.ui.model.LoginInfoState
import com.forthosewho.app.platform.utils.EnvironmentSelection
import com.forthosewho.app.platform.utils.empty
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.stringTemplate
import kotlinx.coroutines.launch

class LoginInfoViewModel(
    private val authLoginUseCase: AuthLoginUseCase,
    private val meUseCase: MeUseCase,
    private val analyticsUseCase: AnalyticsUseCase,
    private val validateEmailUseCase: ValidateEmailUseCase = ValidateEmailUseCase(),
) : ViewModel() {

    private val _state = MutableStateFlow(LoginInfoState.State.createInitialState())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        checkVisibilityOfBatcave()
        analyticsUseCase.trackEvent(UserOnboardingSignupEvents.Login())
        _state.update { state -> state.copy(screenState = LoginInfoState.ScreenState.StateLogin) }
    }

    private fun checkVisibilityOfBatcave() {
        viewModelScope.launch {
            val isDebug = EnvironmentSelection.isDebug
            _state.update { state -> state.copy(isButtonEnabled = isDebug) }
        }
    }

    fun onEvent(state: LoginInfoState.State) {
        if (state.screenState is LoginInfoState.ScreenState.StateLogin) {
            when (state.formState) {
                is LoginInfoState.FormState.StateFormEmpty -> {
                    _state.update { state.copy(infoState = LoginInfoState.LoginInfo()) }
                }

                is LoginInfoState.FormState.StateEmailChanged -> {
                    _state.update {
                        state.copy(
                            infoState = _state.value.infoState.copy(
                                email = state.infoState.email,
                                emailError = null
                            )
                        )
                    }
                }

                is LoginInfoState.FormState.StatePasswordChanged -> {
                    _state.update {
                        state.copy(
                            infoState = _state.value.infoState.copy(
                                password = state.infoState.password,
                                passwordError = null
                            )
                        )
                    }
                }

                is LoginInfoState.FormState.StateVisiblePassword -> {
                    _state.update {
                        state.copy(
                            infoState = _state.value.infoState.copy(isVisiblePassword = state.infoState.isVisiblePassword)
                        )
                    }
                }
            }
        }
    }

    private fun validateEmail(email: String): Boolean {
        val emailResult = validateEmailUseCase(ValidateEmailUseCase.Params(email = email))
        _state.update { state -> state.copy(infoState = _state.value.infoState.copy(emailError = emailResult.errorResource)) }
        return emailResult.successful
    }


    private fun executeLogin(email: String, password: String) {
        _state.update { state -> state.copy(screenState = LoginInfoState.ScreenState.StateLoading) }
        viewModelScope.launch {
            authLoginUseCase.invoke(AuthLoginUseCase.Params(email = email, password = password)).onSuccess {
                meUseCase.invoke().onSuccess { me ->
                    when (me.status) {
                        Me.Status.INVITED -> {
                            _state.update { state -> state.copy(screenState = LoginInfoState.ScreenState.StateLogin) }
                        }

                        Me.Status.VERIFIED -> {
                            _state.update { state -> state.copy(screenState = LoginInfoState.ScreenState.StateLoginSuccessWithoutClues) }
                        }

                        Me.Status.ONBOARDED -> {
                            _state.update { state -> state.copy(screenState = LoginInfoState.ScreenState.StateLoginSuccess) }
                        }

                        Me.Status.NOT_ONBOARDED -> {
                            _state.update { state -> state.copy(screenState = LoginInfoState.ScreenState.StateNotOnboarded) }
                        }
                    }
                }.onFailure {
                    _state.update { state ->
                        state.copy(
                            screenState = LoginInfoState.ScreenState.StateLoginFailure,
                            infoState = _state.value.infoState.copy(password = String.empty(), email = String.empty())
                        )
                    }
                }
            }.onFailure { error ->
                val errorDetails = error.details?.errors?.get(0) ?: String.empty()
                _state.update { state ->
                    val validationResult = ValidationResult(
                        successful = false,
                        errorResource = Res.string.stringTemplate,
                        errorMessage = errorDetails
                    )
                    state.copy(
                        screenState = LoginInfoState.ScreenState.StateLoginFailure,
                        infoState = _state.value.infoState.copy(
                            password = String.empty(),
                            email = String.empty(),
                            passwordError = validationResult.errorResource,
                            passwordServerError = validationResult.errorMessage
                        )
                    )
                }
            }
        }
    }

    fun onLoginButtonClicked(email: String, password: String) {
        if (validateEmail(email)) {
            executeLogin(email, password)
        }
    }
}
