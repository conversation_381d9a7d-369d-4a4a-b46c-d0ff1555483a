package com.forthosewho.app.login.domain.model

import com.forthosewho.app.platform.utils.empty

data class Token(
    val accessToken: String,
    val accessTokenExpiresAt: String,
    val refreshToken: String,
    val refreshTokenExpiresAt: String
) {
    companion object {
        val emptyToken = Token(
            accessToken = String.empty(),
            accessTokenExpiresAt = String.empty(),
            refreshToken = String.empty(),
            refreshTokenExpiresAt = String.empty(),
        )
    }
}