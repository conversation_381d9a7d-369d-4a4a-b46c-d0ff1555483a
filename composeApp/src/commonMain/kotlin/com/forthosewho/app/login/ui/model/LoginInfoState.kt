package com.forthosewho.app.login.ui.model

import com.forthosewho.app.platform.utils.empty
import org.jetbrains.compose.resources.StringResource

object LoginInfoState {
    data class State(
        val screenState: ScreenState,
        val formState: FormState,
        val infoState: LoginInfo,
        val isButtonEnabled: Boolean = false
    ) {
        companion object {
            fun createInitialState(): State = State(
                screenState = ScreenState.StateInitial,
                formState = FormState.StateFormEmpty,
                infoState = LoginInfo(),
                isButtonEnabled = false
            )
        }

    }

    sealed class ScreenState {
        data object StateInitial : ScreenState()
        data object StateLogin : ScreenState()
        data object StateLoginSuccess : ScreenState()
        data object StateNotOnboarded : ScreenState()
        data object StateLoginSuccessWithoutClues : ScreenState()
        data object StateLoginFailure : ScreenState()
        data object StateLoading : ScreenState()
    }

    sealed class FormState {
        data object StateFormEmpty : FormState()
        data object StateEmailChanged : FormState()
        data object StatePasswordChanged : FormState()
        data object StateVisiblePassword : FormState()
    }

    data class LoginInfo(
        val email: String = String.empty(),
        val emailError: StringResource? = null,
        val password: String = String.empty(),
        val passwordError: StringResource? = null,
        val passwordServerError: String? = null,
        val isVisiblePassword: Boolean = false,
        val buttonEnabled: Boolean = false
    ) {
        fun isButtonEnabled(): Boolean = email.isNotEmpty() && password.isNotEmpty()
    }
}
