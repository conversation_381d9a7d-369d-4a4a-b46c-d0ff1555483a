package com.forthosewho.app.login.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.analytics.values.token
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.ClearStoredValuesUseCase
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.login.domain.model.Me
import com.forthosewho.app.login.domain.usecase.AuthLoginUseCase
import com.forthosewho.app.login.domain.usecase.MeUseCase
import com.forthosewho.app.login.ui.model.Intro
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class LoginViewModel(
    private val authLoginUseCase: AuthLoginUseCase,
    private val meUseCase: MeUseCase,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val clearStoredValuesUseCase: ClearStoredValuesUseCase = ClearStoredValuesUseCase(),
    private val analyticsUseCase: AnalyticsUseCase,
) : ViewModel() {

    private val _state = MutableStateFlow(Intro.State())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        checkIfLoggedIn()
        analyticsUseCase.initialize(token)
    }

    private fun checkIfLoggedIn() {
        viewModelScope.launch {
            val isLoggedIn = getStoredValueUseCase.getBoolean(key = DataStoreValues.LOGGED_IN, defaultVal = false)
            if (isLoggedIn) {
                executeLogin()
            } else {
                clearStoredValuesUseCase.invoke()
                _state.update { Intro.State(screenState = Intro.ScreenState.StateLoggedOut) }
            }
        }
    }

    private fun executeLogin() {
        viewModelScope.launch {
            authLoginUseCase.invoke().onSuccess {
                meUseCase.invoke().onSuccess { me ->
                    when (me.status) {
                        Me.Status.VERIFIED -> {
                            _state.update { Intro.State(screenState = Intro.ScreenState.StateAssistantOnboarding) }
                        }

                        Me.Status.INVITED -> {
                            _state.update { Intro.State(screenState = Intro.ScreenState.StateLoggedOut) }
                        }

                        Me.Status.ONBOARDED -> {
                            _state.update { Intro.State(screenState = Intro.ScreenState.StateLoggedIn) }
                        }

                        Me.Status.NOT_ONBOARDED -> {
                            _state.update { Intro.State(screenState = Intro.ScreenState.StateLoggedOut) }
                        }
                    }
                }
            }.onFailure {
                _state.update { state -> state.copy(screenState = Intro.ScreenState.StateLoggedOut) }
            }
        }
    }
}
