package com.forthosewho.app.login.ui.model

import com.forthosewho.app.platform.utils.empty
import org.jetbrains.compose.resources.StringResource

object RegisterState {
    //static variable to keep the existing register info
    internal var registerInfo = RegisterInfo()

    data class State(
        val screenState: ScreenState,
        val infoState: RegisterInfo,
        val formState: FormState,
        val isButtonEnabled: Boolean = false
    ) {
        companion object {
            fun createInitialState(): State = State(
                screenState = ScreenState.StateInitial,
                infoState = RegisterInfo.createRegisterInfo(),
                formState = FormState.StateFormEmpty,
                isButtonEnabled = false
            )
        }
    }

    sealed class ScreenState {
        data object StateInitial : ScreenState()
        data object StateNavigateToLogin : ScreenState()
        data object StateRegister : ScreenState()
        data object StateRegisterSuccess : ScreenState()
        data object StateRegisterFailure : ScreenState()
        data object StateLoading : ScreenState()
    }

    sealed class FormState {
        data object StateFormEmpty : FormState()
        data object StateFormPrefilled : FormState()
        data object StateNameChanged : FormState()
        data object StateEmailChanged : FormState()
        data object StatePasswordChanged : FormState()
        data object StateVisiblePassword : FormState()
    }

    data class RegisterInfo(
        var name: String = String.empty(),
        var email: String = String.empty(),
        var password: String = String.empty(),
        val emailError: StringResource? = null,
        val passwordError: StringResource? = null,
        val passwordServerError: String? = null,
        val isVisiblePassword: Boolean = false,
    ) {
        fun hasStoredValues(): Boolean = name.isNotEmpty() && email.isNotEmpty()

        fun isButtonEnabled(): Boolean = name.isNotEmpty() && email.isNotEmpty() && password.isNotEmpty()

        companion object {
            fun createRegisterInfo(): RegisterInfo = RegisterInfo(
                name = registerInfo.name,
                email = registerInfo.email,
            )

            fun resetRegisterInfo() {
                registerInfo = RegisterInfo()
            }
        }
    }
}
