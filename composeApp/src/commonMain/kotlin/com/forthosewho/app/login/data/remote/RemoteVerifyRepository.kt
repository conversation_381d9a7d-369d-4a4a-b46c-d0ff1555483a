package com.forthosewho.app.login.data.remote

import com.forthosewho.app.login.data.entity.VerifyResponseRaw
import com.forthosewho.app.login.domain.VerifyRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Verify
import com.forthosewho.app.network.data.entity.ResponseRaw
import com.forthosewho.app.network.domain.HTTPS
import com.forthosewho.app.network.submitFormRequest
import com.github.michaelbull.result.Result
import com.forthosewho.app.platform.utils.empty
import io.ktor.client.HttpClient
import io.ktor.http.Parameters

private const val VERIFY_API = "/api/users/verify"
private const val USERNAME = "username"
private const val VERIFICATION_CODE = "verificationCode"

class RemoteVerifyRepository(private val client: HttpClient, private val authUrl: String) : VerifyRepository {

    override suspend fun postVerify(
        username: String,
        verificationCode: String
    ): Result<Verify, ErrorResponse> {
        return client.submitFormRequest<ResponseRaw<VerifyResponseRaw>, Verify>(
            url = "$HTTPS://$authUrl$VERIFY_API",
            formParameters = Parameters.build {
                append(USERNAME, username)
                append(VERIFICATION_CODE, verificationCode)
            },
            onSuccess = { tokenRaw ->
                tokenRaw?.data?.toVerify() ?: Verify(
                    username = String.empty(),
                    password = String.empty(),
                    metadata = String.empty(),
                )
            },
            onError = { errorResponse -> errorResponse }
        )
    }
}
