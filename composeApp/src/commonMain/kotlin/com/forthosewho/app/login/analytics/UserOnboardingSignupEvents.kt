package com.forthosewho.app.login.analytics

import com.forthosewho.app.analytics.AnalyticsEvent

sealed class UserOnboardingSignupEvents {

    data class SignupScreen(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Signup Screen"
    }

    data class SignupStarted(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Signup Started"
    }

    data class VerificationCodeInserted(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Verification Code Inserted"
    }

    data class Login(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Login"
    }

    data class Logout(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Logout"
    }
}