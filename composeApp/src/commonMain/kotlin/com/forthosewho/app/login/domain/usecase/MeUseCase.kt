package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase.Params
import com.forthosewho.app.login.data.remote.RemoteMeRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Me
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess

class MeUseCase(
    private val meRepository: RemoteMeRepository,
    private val setStoredValueUseCase: SetStoredValueUseCase = SetStoredValueUseCase(),
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val analyticsUseCase: AnalyticsUseCase
) {
    suspend operator fun invoke(): Result<Me, ErrorResponse> =
        invoke(params = Params(email = getEmail(), password = getPassword()))

    suspend operator fun invoke(params: Params): Result<Me, ErrorResponse> {
        return meRepository.getMe(getToken()).onSuccess { me ->
            analyticsUseCase.identify(userID = me.userId)
            setStoredValueUseCase.apply {
                invoke(Params(dataStoreValue = DataStoreValues.EMAIL, value = params.email))
                invoke(Params(dataStoreValue = DataStoreValues.PASSWORD, value = params.password))
                invoke(Params(dataStoreValue = DataStoreValues.USER_ID, value = me.userId))
                invoke(Params(dataStoreValue = DataStoreValues.NAME, value = me.fullName))
                invoke(Params(dataStoreValue = DataStoreValues.LOGGED_IN, value = true))
            }
        }.onFailure {
            setStoredValueUseCase.apply { invoke(Params(dataStoreValue = DataStoreValues.LOGGED_IN, value = false)) }
        }
    }

    private suspend fun getEmail(): String = getStoredValueUseCase.getString(key = DataStoreValues.EMAIL)
    private suspend fun getToken(): String = getStoredValueUseCase.getString(key = DataStoreValues.ACCESS_TOKEN)
    private suspend fun getPassword(): String = getStoredValueUseCase.getString(key = DataStoreValues.PASSWORD)

    data class Params(
        val email: String,
        val password: String,
    )
}
