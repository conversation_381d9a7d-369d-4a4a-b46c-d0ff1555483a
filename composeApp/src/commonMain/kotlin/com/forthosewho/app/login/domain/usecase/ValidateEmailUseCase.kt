package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.login.domain.model.ValidationResult
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.please_enter_a_valid_email
import forthosewho_app.composeapp.generated.resources.this_is_not_a_valid_email

private const val EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,6}$"

class ValidateEmailUseCase {
    operator fun invoke(params: Params): ValidationResult {
        if (params.email.isBlank()) {
            return ValidationResult(
                successful = false,
                errorResource = Res.string.please_enter_a_valid_email
            )
        } else if (!isValidEmail(params.email)) {
            return ValidationResult(
                successful = false,
                errorResource = Res.string.this_is_not_a_valid_email
            )
        } else {
            return ValidationResult(
                successful = true,
                errorResource = null,
            )
        }
    }

    data class Params(val email: String)

    private fun isValidEmail(email: String): Boolean {
        val emailRegex = EMAIL_REGEX.toRegex()
        return emailRegex.matches(email)
    }
}
