package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase.Params
import com.forthosewho.app.login.data.remote.RemoteAuthLoginRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Token
import com.github.michaelbull.result.onSuccess
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.onFailure

class AuthLoginUseCase(
    private val remoteAuthLoginRepository: RemoteAuthLoginRepository,
    private val setStoredValueUseCase: SetStoredValueUseCase = SetStoredValueUseCase(),
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
) {
    suspend operator fun invoke(): Result<Token, ErrorResponse> =
        invoke(params = Params(email = getEmail(), password = getPassword()))

    suspend operator fun invoke(params: Params): Result<Token, ErrorResponse> {
        return remoteAuthLoginRepository.authLogin(username = params.email, password = params.password).onSuccess { token ->
            setStoredValueUseCase.apply {
                invoke(Params(dataStoreValue = DataStoreValues.EMAIL, value = params.email))
                invoke(Params(dataStoreValue = DataStoreValues.PASSWORD, value = params.password))
                invoke(Params(dataStoreValue = DataStoreValues.ACCESS_TOKEN, value = token.accessToken))
                invoke(Params(dataStoreValue = DataStoreValues.REFRESH_TOKEN, value = token.refreshToken))
                invoke(Params(dataStoreValue = DataStoreValues.LOGGED_IN, value = true))
            }
        }.onFailure {
            setStoredValueUseCase.apply { invoke(Params(dataStoreValue = DataStoreValues.LOGGED_IN, value = false)) }
        }
    }

    private suspend fun getEmail(): String = getStoredValueUseCase.getString(key = DataStoreValues.EMAIL)

    private suspend fun getPassword(): String = getStoredValueUseCase.getString(key = DataStoreValues.PASSWORD)

    data class Params(
        val email: String,
        val password: String,
    )
}
