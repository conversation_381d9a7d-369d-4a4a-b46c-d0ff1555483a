package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.login.domain.model.ValidationResult
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.password_at_least_8_characters
import forthosewho_app.composeapp.generated.resources.password_at_least_one_letter_and_one_digit
import forthosewho_app.composeapp.generated.resources.stringTemplate

class ValidatePasswordUseCase {
    operator fun invoke(params: Params): ValidationResult {
        if (params.serverError != null) {
            return ValidationResult(
                successful = false,
                errorResource = Res.string.stringTemplate,
                errorMessage = params.serverError
            )
        } else if (params.password.length < 12) {
            return ValidationResult(
                successful = false,
                errorResource = Res.string.password_at_least_8_characters,
            )
        } else if (!isPasswordValid(params.password)) {
            return ValidationResult(
                successful = false,
                errorResource = Res.string.password_at_least_one_letter_and_one_digit,
            )
        } else return ValidationResult(successful = true)
    }

    data class Params(val password: String, val serverError: String? = null)

    private fun isPasswordValid(password: String): Boolean {
        return password.any { it.isDigit() } && password.any { it.isLetter() }
    }
}