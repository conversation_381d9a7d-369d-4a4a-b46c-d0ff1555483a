package com.forthosewho.app.login.data.remote

import com.forthosewho.app.login.data.entity.TokenRaw
import com.forthosewho.app.login.domain.AuthLoginRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Token
import com.forthosewho.app.network.domain.HTTPS
import com.forthosewho.app.network.submitFormRequest
import com.github.michaelbull.result.Result
import io.ktor.client.HttpClient
import io.ktor.http.Parameters

private const val LOGIN_API = "/api/users/login"
private const val PASSWORD = "password"
private const val USERNAME = "username"

class RemoteAuthLoginRepository(private val client: HttpClient, private val authUrl: String) : AuthLoginRepository {

    override suspend fun authLogin(username: String, password: String): Result<Token, ErrorResponse> {
        return client.submitFormRequest<TokenRaw, Token>(
            url = "$HTTPS://$authUrl$LOGIN_API",
            formParameters = Parameters.build {
                append(USERNAME, username)
                append(PASSWORD, password)
            },
            onSuccess = { tokenRaw -> tokenRaw?.toToken() ?: Token.emptyToken },
            onError = { errorMessage -> errorMessage }
        )
    }
}
