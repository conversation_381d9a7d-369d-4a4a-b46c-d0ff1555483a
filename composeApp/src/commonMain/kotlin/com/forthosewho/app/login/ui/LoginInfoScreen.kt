package com.forthosewho.app.login.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.forthosewho.app.login.ui.model.LoginInfoState
import com.forthosewho.app.login.ui.viewmodel.LoginInfoViewModel
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Blue25
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.GreySuperLight
import com.forthosewho.app.theme.colors.Red
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.icon.FtwIcons.Eye
import com.forthosewho.app.theme.icon.FtwIcons.EyeOff
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.account_name_or_email
import forthosewho_app.composeapp.generated.resources.email_placeholder_example
import forthosewho_app.composeapp.generated.resources.loading_log_in
import forthosewho_app.composeapp.generated.resources.log_in
import forthosewho_app.composeapp.generated.resources.logo_white
import forthosewho_app.composeapp.generated.resources.password
import forthosewho_app.composeapp.generated.resources.password_placeholder_example
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Preview
@Composable
internal fun LoginInfoScreen(
    loginInfoViewModel: LoginInfoViewModel = koinViewModel<LoginInfoViewModel>(),
    onStoriesScreenNavigated: () -> Unit,
    onAssistantOnboardingScreenNavigated: () -> Unit,
    onBatcaveScreenNavigated: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val state by loginInfoViewModel.state.collectAsState()

    FTWLoggedOutContent(
        content = {
            when (state.screenState) {
                is LoginInfoState.ScreenState.StateInitial -> {
                    loginInfoViewModel.onStateInitial()
                }
                is LoginInfoState.ScreenState.StateLoginSuccess -> {
                    onStoriesScreenNavigated()
                }

                is LoginInfoState.ScreenState.StateLoginSuccessWithoutClues -> {
                    onAssistantOnboardingScreenNavigated()
                }

                is LoginInfoState.ScreenState.StateLoading -> {
                    LoginLoadingState()
                }

                is LoginInfoState.ScreenState.StateLoginFailure,
                is LoginInfoState.ScreenState.StateLogin -> {
                    LoginForm(
                        modifier = Modifier,
                        loginInfoViewModel = loginInfoViewModel,
                    )
                }

                is LoginInfoState.ScreenState.StateNotOnboarded -> {
                    onAssistantOnboardingScreenNavigated()
                }
            }
        },
        isButtonClickable = state.isButtonEnabled,
        onBatcaveScreenNavigated = onBatcaveScreenNavigated
    )
}

@Composable
fun FTWLoggedOutContent(
    content: @Composable () -> Unit = {},
    isButtonClickable: Boolean = false,
    onBatcaveScreenNavigated: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .background(White)
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Box(modifier = Modifier.background(Blue).fillMaxWidth().weight(0.10F))
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .clip(RoundedCornerShape(bottomEnd = 40.dp, bottomStart = 40.dp))
                .background(Blue)
                .fillMaxWidth()
                .weight(0.25F),
        ) {
            Image(
                modifier = Modifier.wrapContentSize().clickable(
                    enabled = isButtonClickable,
                    onClick = onBatcaveScreenNavigated
                ),
                alignment = Alignment.Center,
                painter = painterResource(Res.drawable.logo_white),
                contentDescription = null,
            )
        }
        Column(modifier = Modifier.weight(0.70F).fillMaxWidth().padding(horizontal = 24.dp)) {
            content()
        }
    }
}

@Composable
fun LoginLoadingState() {
    Column(
        modifier = Modifier
            .padding(horizontal = 40.dp, vertical = 40.dp)
            .background(White)
            .fillMaxSize()
            .verticalScroll(rememberScrollState()),
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(60.dp).align(Alignment.CenterHorizontally),
            strokeWidth = 5.dp,
            color = Blue
        )
        Text(
            text = stringResource(Res.string.loading_log_in),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelLarge,
            modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(top = 24.dp),
        )
    }
}

@Composable
fun LoginForm(
    modifier: Modifier,
    loginInfoViewModel: LoginInfoViewModel,
) {
    val state by loginInfoViewModel.state.collectAsState()

    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }

    val scrollState = rememberScrollState()

    Column(
        modifier = modifier.verticalScroll(scrollState),
        verticalArrangement = Arrangement.Top
    ) {
        Spacer(modifier.padding(top = 36.dp))
        FormFieldTemplate(
            isError = state.infoState.emailError != null,
            modifier = Modifier,
            text = { isError ->
                Text(
                    text = stringResource(Res.string.account_name_or_email),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isError) {
                        Red
                    } else {
                        Black
                    },
                )
            },
            textFieldTemplate = { isError ->
                OutlinedTextFieldTemplate(
                    modifier = modifier,
                    value = email,
                    placeholderText = stringResource(Res.string.email_placeholder_example),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Email,
                        imeAction = ImeAction.Next,
                        autoCorrect = true,
                        capitalization = KeyboardCapitalization.None
                    ),
                    supportingText = {
                        state.infoState.emailError?.let {
                            Text(
                                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(top = 10.dp),
                                text = stringResource(it),
                                textAlign = TextAlign.Start,
                                style = MaterialTheme.typography.labelMedium,
                                color = GreyLight
                            )
                        }
                    },
                    isError = isError,
                    onValueChange = {
                        email = it
                        loginInfoViewModel.onEvent(
                            state = LoginInfoState.State(
                                screenState = LoginInfoState.ScreenState.StateLogin,
                                formState = LoginInfoState.FormState.StateEmailChanged,
                                infoState = LoginInfoState.LoginInfo(email = email),
                            )
                        )
                    },
                )
            }
        )
        Spacer(modifier.padding(top = 12.dp))
        FormFieldTemplate(
            isError = state.infoState.passwordError != null,
            modifier = Modifier,
            text = { isError ->
                Text(
                    text = stringResource(Res.string.password),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isError) {
                        Red
                    } else {
                        Black
                    },
                )
            },
            textFieldTemplate = {
                OutlinedTextFieldTemplate(
                    modifier = modifier,
                    value = password,
                    placeholderText = stringResource(Res.string.password_placeholder_example),
                    supportingText = {
                        state.infoState.passwordError?.let {
                            Text(
                                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(top = 10.dp),
                                text = state.infoState.passwordServerError?.let { it1 ->
                                    stringResource(it, it1)
                                } ?: stringResource(it),
                                textAlign = TextAlign.Start,
                                style = MaterialTheme.typography.labelMedium,
                                color = GreyLight
                            )
                        }
                    },
                    isError = state.infoState.passwordError != null,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password, imeAction = ImeAction.Done),
                    trailingIcon = {
                        IconButton(
                            onClick = {
                                loginInfoViewModel.onEvent(
                                    state = LoginInfoState.State(
                                        screenState = LoginInfoState.ScreenState.StateLogin,
                                        formState = LoginInfoState.FormState.StateVisiblePassword,
                                        infoState = LoginInfoState.LoginInfo(isVisiblePassword = !state.infoState.isVisiblePassword)
                                    )
                                )
                            },
                        ) {
                            if (state.infoState.isVisiblePassword) {
                                Icon(
                                    imageVector = Eye,
                                    contentDescription = null
                                )
                            } else {
                                Icon(
                                    imageVector = EyeOff,
                                    contentDescription = null
                                )
                            }
                        }
                    },
                    onValueChange = {
                        password = it
                        if (password.isNotEmpty()) {
                            loginInfoViewModel.onEvent(
                                state = LoginInfoState.State(
                                    screenState = LoginInfoState.ScreenState.StateLogin,
                                    formState = LoginInfoState.FormState.StatePasswordChanged,
                                    infoState = LoginInfoState.LoginInfo(password = password)
                                )
                            )
                        }
                    },
                    visualTransformation = if (!state.infoState.isVisiblePassword) {
                        PasswordVisualTransformation()
                    } else {
                        VisualTransformation.None
                    }
                )
            }
        )
        Spacer(modifier.padding(top = 48.dp))
        ButtonTemplate(
            modifier = Modifier,
            enabled = state.infoState.isButtonEnabled(),
            buttonText = stringResource(Res.string.log_in),
            onClick = { loginInfoViewModel.onLoginButtonClicked(email, password) }
        )
    }
}

@Composable
fun FormFieldTemplate(
    modifier: Modifier,
    text: @Composable (Boolean) -> Unit = {},
    isError: Boolean = false,
    textFieldTemplate: @Composable (Boolean) -> Unit = {}
) {
    Column {
        Spacer(modifier.padding(top = 12.dp))
        text(isError)
        Spacer(modifier.padding(top = 8.dp))
        textFieldTemplate(isError)
        Spacer(modifier.padding(top = 12.dp))
    }
}

@Composable
fun OutlinedTextFieldTemplate(
    modifier: Modifier = Modifier,
    onValueChange: (String) -> Unit,
    value: String,
    placeholderText: String,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    supportingText: @Composable () -> Unit = {},
    trailingIcon: @Composable () -> Unit = {},
    isError: Boolean = false,
    keyboardOptions: KeyboardOptions = KeyboardOptions(
        keyboardType = KeyboardType.Text,
        imeAction = ImeAction.Done,
        autoCorrect = true,
        capitalization = KeyboardCapitalization.Words
    ),
) {
    OutlinedTextField(
        colors = OutlinedTextFieldDefaults.colors(
            unfocusedTextColor = Black,
            unfocusedBorderColor = GreySuperLight,
            unfocusedLabelColor = GreySuperLight,
            unfocusedLeadingIconColor = GreySuperLight,
            focusedTextColor = Black,
            focusedBorderColor = GreyLight,
            focusedLabelColor = GreyLight,
            focusedLeadingIconColor = GreyLight,
            errorTextColor = Red,
            errorBorderColor = Red,
            errorLabelColor = Red,
            errorLeadingIconColor = Red,
        ),
        value = value,
        onValueChange = { onValueChange(it) },
        placeholder = {
            Text(
                text = placeholderText,
                style = MaterialTheme.typography.labelLarge,
                color = GreyLight,
                textAlign = TextAlign.Start
            )
        },
        visualTransformation = visualTransformation,
        trailingIcon = { trailingIcon() },
        singleLine = true,
        isError = isError,
        shape = RoundedCornerShape(8.dp),
        keyboardOptions = keyboardOptions,
        modifier = modifier.fillMaxWidth().wrapContentHeight(),
    )
    supportingText()
}

@Composable
fun ButtonTemplate(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    buttonText: String,
    onClick: () -> Unit = {},
) {
    Button(
        onClick = { onClick() },
        enabled = enabled,
        modifier = modifier.fillMaxWidth().wrapContentHeight(),
        shape = RoundedCornerShape(12.dp),
        colors = ButtonColors(
            containerColor = Blue,
            contentColor = White,
            disabledContainerColor = Blue25,
            disabledContentColor = White,
        ),
        contentPadding = PaddingValues(16.dp)
    ) {
        Text(
            text = buttonText,
            color = White,
            style = MaterialTheme.typography.headlineMedium,
        )
    }
}