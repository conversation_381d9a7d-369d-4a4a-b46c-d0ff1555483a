package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase.Params
import com.forthosewho.app.login.data.remote.RemoteTokenRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Token
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess

class TokenUseCase(
    private val remoteTokenRepository: RemoteTokenRepository,
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val setStoredValueUseCase: SetStoredValueUseCase = SetStoredValueUseCase()
) {

    suspend operator fun invoke(): Result<Token,ErrorResponse> {
        return remoteTokenRepository.issueToken(refreshToken = getStoredValueUseCase.getString(DataStoreValues.REFRESH_TOKEN))
            .onSuccess {
                setStoredValueUseCase.apply {
                    invoke(Params(dataStoreValue = DataStoreValues.ACCESS_TOKEN, value = it.accessToken))
                    invoke(Params(dataStoreValue = DataStoreValues.REFRESH_TOKEN, value = it.refreshToken))
                    invoke(Params(dataStoreValue = DataStoreValues.LOGGED_IN, value = true))
                }
            }.onFailure {
                setStoredValueUseCase.apply { invoke(Params(dataStoreValue = DataStoreValues.LOGGED_IN, value = false)) }
            }
    }
}