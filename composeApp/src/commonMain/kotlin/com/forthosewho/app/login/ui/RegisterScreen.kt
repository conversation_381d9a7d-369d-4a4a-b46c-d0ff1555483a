package com.forthosewho.app.login.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.forthosewho.app.login.ui.model.RegisterState
import com.forthosewho.app.login.ui.viewmodel.RegisterViewmodel
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.Red
import com.forthosewho.app.theme.icon.FtwIcons.Eye
import com.forthosewho.app.theme.icon.FtwIcons.EyeOff
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.already_have_an_account
import forthosewho_app.composeapp.generated.resources.email
import forthosewho_app.composeapp.generated.resources.email_placeholder_example
import forthosewho_app.composeapp.generated.resources.full_name
import forthosewho_app.composeapp.generated.resources.full_name_placeholder_example
import forthosewho_app.composeapp.generated.resources.password
import forthosewho_app.composeapp.generated.resources.password_placeholder_example
import forthosewho_app.composeapp.generated.resources.sign_up
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Preview
@Composable
fun RegisterScreen(
    registerViewmodel: RegisterViewmodel = koinViewModel<RegisterViewmodel>().apply { onStateInitial() },
    onAlreadyAUserButtonNavigated: () -> Unit,
    onVerifyCodeScreenNavigated: () -> Unit,
    onBatcaveScreenNavigated: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val state by registerViewmodel.state.collectAsState()

    FTWLoggedOutContent(
        content = {
            when (state.screenState) {
                is RegisterState.ScreenState.StateInitial -> {
                    registerViewmodel.onStateInitial()
                }

                is RegisterState.ScreenState.StateRegisterSuccess -> {
                    LoginLoadingState()
                    onVerifyCodeScreenNavigated()
                }

                is RegisterState.ScreenState.StateLoading -> {
                    LoginLoadingState()
                }

                is RegisterState.ScreenState.StateNavigateToLogin -> {
                    onAlreadyAUserButtonNavigated()
                }

                is RegisterState.ScreenState.StateRegisterFailure,
                is RegisterState.ScreenState.StateRegister -> {
                    RegistrationForm(
                        modifier = Modifier,
                        registerViewmodel = registerViewmodel,
                        name = state.infoState.name,
                        email = state.infoState.email
                    )
                }

            }
        },
        isButtonClickable = registerViewmodel.state.value.isButtonEnabled,
        onBatcaveScreenNavigated = onBatcaveScreenNavigated
    )
}

@Composable
fun RegistrationForm(
    modifier: Modifier,
    registerViewmodel: RegisterViewmodel,
    name: String = String.empty(),
    email: String = String.empty(),
) {
    val state by registerViewmodel.state.collectAsState()

    var name by remember { mutableStateOf(name) }
    var email by remember { mutableStateOf(email) }
    var password by remember { mutableStateOf("") }

    val scrollState = rememberScrollState()

    Column(
        modifier = modifier.verticalScroll(scrollState),
        verticalArrangement = Arrangement.Top
    ) {
        Spacer(modifier.padding(top = 36.dp))
        FormFieldTemplate(
            modifier = Modifier,
            text = {
                Text(
                    text = stringResource(Res.string.full_name),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.labelLarge,
                )
            },
            textFieldTemplate = {
                OutlinedTextFieldTemplate(
                    modifier = modifier,
                    value = name,
                    placeholderText = stringResource(Res.string.full_name_placeholder_example),
                    onValueChange = {
                        name = it
                        registerViewmodel.onEvent(
                            state = RegisterState.State(
                                screenState = RegisterState.ScreenState.StateRegister,
                                formState = RegisterState.FormState.StateNameChanged,
                                infoState = RegisterState.RegisterInfo(name = name),
                            )
                        )
                    },
                )
            }
        )
        Spacer(modifier.padding(top = 12.dp))
        FormFieldTemplate(
            isError = state.infoState.emailError != null,
            modifier = Modifier,
            text = { isError ->
                Text(
                    text = stringResource(Res.string.email),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isError) {
                        Red
                    } else {
                        Black
                    },
                )
            },
            textFieldTemplate = { isError ->
                OutlinedTextFieldTemplate(
                    modifier = modifier,
                    isError = isError,
                    value = email,
                    placeholderText = stringResource(Res.string.email_placeholder_example),
                    supportingText = {
                        state.infoState.emailError?.let {
                            Text(
                                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(top = 10.dp),
                                text = stringResource(it),
                                textAlign = TextAlign.Start,
                                style = MaterialTheme.typography.labelMedium,
                                color = GreyLight
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Email,
                        imeAction = ImeAction.Done,
                        autoCorrect = true,
                        capitalization = KeyboardCapitalization.None
                    ),
                    onValueChange = {
                        email = it
                        registerViewmodel.onEvent(
                            state = RegisterState.State(
                                screenState = RegisterState.ScreenState.StateRegister,
                                formState = RegisterState.FormState.StateEmailChanged,
                                infoState = RegisterState.RegisterInfo(email = email),
                            )
                        )
                    },
                )
            }
        )
        Spacer(modifier.padding(top = 12.dp))
        FormFieldTemplate(
            isError = state.infoState.passwordError != null,
            modifier = Modifier,
            text = { isError ->
                Text(
                    text = stringResource(Res.string.password),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isError) {
                        Red
                    } else {
                        Black
                    },
                )
            },
            textFieldTemplate = { isError ->
                OutlinedTextFieldTemplate(
                    modifier = modifier,
                    value = password,
                    isError = isError,
                    placeholderText = stringResource(Res.string.password_placeholder_example),
                    supportingText = {
                        state.infoState.passwordError?.let {
                            Text(
                                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(top = 10.dp),
                                text = state.infoState.passwordServerError?.let { it1 -> stringResource(it, it1) }
                                    ?: stringResource(it),
                                textAlign = TextAlign.Start,
                                style = MaterialTheme.typography.labelMedium,
                                color = GreyLight
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password, imeAction = ImeAction.Done),
                    trailingIcon = {
                        IconButton(
                            onClick = {
                                registerViewmodel.onEvent(
                                    state = RegisterState.State(
                                        screenState = RegisterState.ScreenState.StateRegister,
                                        formState = RegisterState.FormState.StateVisiblePassword,
                                        infoState = RegisterState.RegisterInfo(isVisiblePassword = !state.infoState.isVisiblePassword)
                                    )
                                )
                            },
                        ) {
                            if (state.infoState.isVisiblePassword) {
                                Icon(
                                    imageVector = Eye,
                                    contentDescription = null
                                )
                            } else {
                                Icon(
                                    imageVector = EyeOff,
                                    contentDescription = null
                                )
                            }
                        }
                    },
                    onValueChange = {
                        password = it
                        if (password.isNotEmpty()) {
                            registerViewmodel.onEvent(
                                state = RegisterState.State(
                                    screenState = RegisterState.ScreenState.StateRegister,
                                    formState = RegisterState.FormState.StatePasswordChanged,
                                    infoState = RegisterState.RegisterInfo(password = password),
                                )
                            )
                        }
                    },
                    visualTransformation = if (!state.infoState.isVisiblePassword) {
                        PasswordVisualTransformation()
                    } else {
                        VisualTransformation.None
                    }
                )
            }
        )
        Spacer(modifier.padding(top = 18.dp))
        ButtonTemplate(
            buttonText = stringResource(Res.string.sign_up),
            enabled = state.infoState.isButtonEnabled(),
            onClick = { registerViewmodel.onSignUpButtonClicked(name, email, password) }
        )
        Spacer(modifier.padding(top = 18.dp))
        Text(
            text = stringResource(Res.string.already_have_an_account),
            color = Blue,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelLarge,
            modifier = Modifier.fillMaxWidth().wrapContentHeight().clickable {
                registerViewmodel.onAlreadyAUserButtonClicked()
            },
        )
    }
}
