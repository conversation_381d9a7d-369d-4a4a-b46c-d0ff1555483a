package com.forthosewho.app.login.ui.model

import com.forthosewho.app.login.ui.model.RegisterState.registerInfo

object VerifyState {
    data class State(
        val screenState: ScreenState,
        val email: String,
        val password: String,
        val isButtonEnabled: Boolean = false
    ) {
        companion object {
            fun createInitialState(): State = State(
                screenState = ScreenState.StateInitial,
                email = registerInfo.email,
                password = registerInfo.password,
                isButtonEnabled = false
            )
        }
    }

    sealed class ScreenState {
        data object StateInitial : ScreenState()
        data object StateLoading : ScreenState()
        data object StateResendCode : ScreenState()
        data class StateVerificationFailed(val message: String) : ScreenState()
        data object StateVerificationSuccess : ScreenState()
        data object StateNavigateToAssistantOnboarding : ScreenState()
    }
}
