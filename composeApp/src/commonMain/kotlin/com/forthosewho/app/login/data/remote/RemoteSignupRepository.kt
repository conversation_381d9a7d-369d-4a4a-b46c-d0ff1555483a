package com.forthosewho.app.login.data.remote

import com.forthosewho.app.login.data.entity.SignupResponseRaw
import com.forthosewho.app.login.domain.SignupRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Signup
import com.forthosewho.app.network.data.entity.ResponseRaw
import com.forthosewho.app.network.domain.HTTPS
import com.forthosewho.app.network.submitFormRequest
import com.forthosewho.app.platform.utils.empty
import io.ktor.client.HttpClient
import com.github.michaelbull.result.Result
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import io.ktor.http.Parameters
import kotlinx.serialization.json.JsonObject

private const val SIGNUP_API = "/api/users/signup"
private const val PASSWORD = "password"
private const val USERNAME = "username"
private const val METADATA = "metadata[name]"

class RemoteSignupRepository(private val client: HttpClient, private val authUrl: String) : SignupRepository {
    override suspend fun postSignup(
        username: String,
        password: String,
        metadata: String,
    ): Result<Signup, ErrorResponse> {
        return client.submitFormRequest<ResponseRaw<SignupResponseRaw>, Signup>(
            url = "$HTTPS://$authUrl$SIGNUP_API",
            formParameters = createParams(
                username = username,
                password = password,
                metadata = metadata
            ),
            onSuccess = { signupRaw ->
                signupRaw?.data?.toSignup() ?: Signup(
                    username = String.empty(),
                    password = String.empty(),
                    metadata = String.empty()
                )
            },
            onError = { errorMessage -> errorMessage }
        )
    }
}

fun createParams(
    username: String,
    password: String,
    metadata: String,
): Parameters {
    val jsonObject: JsonObject = buildJsonObject {
        put(USERNAME, username)
        put(PASSWORD, password)
        put(METADATA, metadata)
    }
    return Parameters.build {
        jsonObject.forEach { (key, value) ->
            val cleanValue = value.toString().removeSurrounding("\"")
            append(key, cleanValue)
        }
    }
}
