package com.forthosewho.app.login.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.login.analytics.UserOnboardingSignupEvents.VerificationCodeInserted
import com.forthosewho.app.login.domain.model.Me
import com.forthosewho.app.login.domain.usecase.AuthLoginUseCase
import com.forthosewho.app.login.domain.usecase.MeUseCase
import com.forthosewho.app.login.domain.usecase.VerifyUseCase
import com.forthosewho.app.login.ui.model.RegisterState.RegisterInfo.Companion.resetRegisterInfo
import com.forthosewho.app.login.ui.model.VerifyState
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class VerifyViewModel(
    private val verifyUseCase: VerifyUseCase,
    private val meUseCase: MeUseCase,
    private val authLoginUseCase: AuthLoginUseCase,
    private val analyticsUseCase: AnalyticsUseCase
) : ViewModel() {

    private val _state = MutableStateFlow(VerifyState.State.createInitialState())
    val state = _state.asStateFlow()

    private fun executeVerifyCode(username: String, verificationCode: String) {
        viewModelScope.launch {
            delay(100L)
            _state.update { state -> state.copy(screenState = VerifyState.ScreenState.StateLoading) }
            verifyUseCase.invoke(VerifyUseCase.Params(username = username, verificationCode = verificationCode)).onSuccess {
                resetRegisterInfo()
                getToken()
            }.onFailure {
                _state.update { state ->
                    state.copy(
                        screenState = VerifyState.ScreenState.StateVerificationFailed(it.details?.errors?.get(0).toString())
                    )
                }
            }
        }
    }

    private suspend fun getToken() {
        authLoginUseCase.invoke(
            params = AuthLoginUseCase.Params(email = state.value.email, password = state.value.password)
        ).onSuccess {
            meUseCase.invoke().onSuccess { me ->
                when (me.status) {
                    Me.Status.NOT_ONBOARDED -> {
                        _state.update { state -> state.copy(screenState = VerifyState.ScreenState.StateNavigateToAssistantOnboarding) }
                    }

                    Me.Status.INVITED -> {
                        _state.update { state -> state.copy(screenState = VerifyState.ScreenState.StateLoading) }
                    }

                    Me.Status.VERIFIED -> {
                        _state.update { state -> state.copy(screenState = VerifyState.ScreenState.StateVerificationSuccess) }
                    }

                    Me.Status.ONBOARDED -> {
                        _state.update { state -> state.copy(screenState = VerifyState.ScreenState.StateLoading) }
                    }
                }
            }
        }.onFailure {
            _state.update { state ->
                state.copy(
                    screenState = VerifyState.ScreenState.StateVerificationFailed(it.details?.errors?.get(0).toString())
                )
            }
        }
    }

    fun onOtpTextChange(otpValue: String) {
        if (otpValue.length == 4) {
            analyticsUseCase.trackEvent(VerificationCodeInserted(properties = mapOf("user_email" to state.value.email)))
            executeVerifyCode(username = state.value.email, verificationCode = otpValue)
        }
    }

    fun onResendEmailClicked() {
        _state.update { state ->
            state.copy(screenState = VerifyState.ScreenState.StateResendCode)
        }
    }

    fun onAssistantOnboarding() {
        _state.update { state ->
            state.copy(screenState = VerifyState.ScreenState.StateNavigateToAssistantOnboarding)
        }
    }

    fun onFocusChange(isFocused: Boolean) {
        if (isFocused) {
            _state.update { state -> state.copy(screenState = VerifyState.ScreenState.StateInitial) }
        }
    }
}