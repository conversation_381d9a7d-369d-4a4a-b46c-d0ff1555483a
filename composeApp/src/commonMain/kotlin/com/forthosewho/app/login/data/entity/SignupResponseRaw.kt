package com.forthosewho.app.login.data.entity

import com.forthosewho.app.login.domain.model.Signup
import com.forthosewho.app.platform.utils.empty
import kotlinx.serialization.Serializable

@Serializable
data class SignupResponseRaw(
    private val username: String? = null,
    private val password: String? = null,
    private val metadata: String? = null,
) {
    fun toSignup() = Signup(
        username = username.toString(),
        password = password.toString(),
        metadata = metadata.toString()
    )

    companion object {
        fun emptySignup() = Signup(
            username = String.empty(),
            password = String.empty(),
            metadata = String.empty()
        )
    }
}
