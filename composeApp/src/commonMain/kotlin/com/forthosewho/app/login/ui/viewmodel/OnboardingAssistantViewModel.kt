package com.forthosewho.app.login.ui.viewmodel

import androidx.lifecycle.ViewModel
import com.forthosewho.app.login.ui.model.OnboardingAssistantState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

class OnboardingAssistantViewModel : ViewModel() {

    private val _state = MutableStateFlow(OnboardingAssistantState.State())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        _state.update {
            OnboardingAssistantState.State(screenState = OnboardingAssistantState.ScreenState.StateOpenAssistant)
        }
    }

    fun closeAssistantAndProceed() {
        _state.update {
            OnboardingAssistantState.State(screenState = OnboardingAssistantState.ScreenState.StateNavigateToStoriesScreen)
        }
    }
}
