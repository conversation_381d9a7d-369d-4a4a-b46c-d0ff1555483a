package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.login.data.remote.RemoteSignupRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Signup
import com.github.michaelbull.result.Result

class SignupUseCase(
    private val remoteSignupRepository: RemoteSignupRepository,
) {
    suspend operator fun invoke(params: Params): Result<Signup,ErrorResponse> {
        return remoteSignupRepository.postSignup(
            username = params.username.trim(),
            password = params.password.trim(),
            metadata = params.name.trim()
        )
    }

    data class Params(
        val username: String,
        val password: String,
        val name: String,
    )
}