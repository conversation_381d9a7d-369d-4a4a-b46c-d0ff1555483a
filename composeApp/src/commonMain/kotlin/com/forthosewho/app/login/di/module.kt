package com.forthosewho.app.login.di

import com.forthosewho.app.login.data.remote.RemoteAuthLoginRepository
import com.forthosewho.app.login.data.remote.RemoteMeRepository
import com.forthosewho.app.login.data.remote.RemoteSignupRepository
import com.forthosewho.app.login.data.remote.RemoteTokenRepository
import com.forthosewho.app.login.data.remote.RemoteVerifyRepository
import com.forthosewho.app.login.domain.usecase.AuthLoginUseCase
import com.forthosewho.app.login.domain.usecase.MeUseCase
import com.forthosewho.app.login.domain.usecase.SignupUseCase
import com.forthosewho.app.login.domain.usecase.TokenUseCase
import com.forthosewho.app.login.domain.usecase.VerifyUseCase
import com.forthosewho.app.login.ui.viewmodel.LoginInfoViewModel
import com.forthosewho.app.login.ui.viewmodel.LoginViewModel
import com.forthosewho.app.login.ui.viewmodel.OnboardingAssistantViewModel
import com.forthosewho.app.login.ui.viewmodel.RegisterViewmodel
import com.forthosewho.app.login.ui.viewmodel.VerifyViewModel
import com.forthosewho.app.network.di.AUTH_CLIENT
import com.forthosewho.app.network.di.CURRENT_AUTH_URL
import com.forthosewho.app.network.di.ME_CLIENT
import org.koin.core.module.dsl.viewModel
import org.koin.core.qualifier.named
import org.koin.dsl.module

val loginModule = module {
    viewModel { LoginViewModel(authLoginUseCase = get(), meUseCase = get(), analyticsUseCase = get()) }
    viewModel { OnboardingAssistantViewModel() }
    viewModel { LoginInfoViewModel(authLoginUseCase = get(), meUseCase = get(), analyticsUseCase = get()) }
    viewModel { RegisterViewmodel(signupUseCase = get(), analyticsUseCase = get()) }
    viewModel {
        VerifyViewModel(
            verifyUseCase = get(),
            meUseCase = get(),
            authLoginUseCase = get(),
            analyticsUseCase = get()
        )
    }

    single { AuthLoginUseCase(remoteAuthLoginRepository = get()) }
    single { MeUseCase(meRepository = get(), analyticsUseCase = get()) }
    single { SignupUseCase(remoteSignupRepository = get()) }
    single { VerifyUseCase(remoteVerifyRepository = get()) }
    single { TokenUseCase(remoteTokenRepository = get()) }

    single { RemoteMeRepository(client = get(named(ME_CLIENT)), authUrl = get(named(CURRENT_AUTH_URL))) }
    single { RemoteAuthLoginRepository(client = get(named(AUTH_CLIENT)), authUrl = get(named(CURRENT_AUTH_URL))) }
    single { RemoteSignupRepository(client = get(named(AUTH_CLIENT)), authUrl = get(named(CURRENT_AUTH_URL))) }
    single { RemoteVerifyRepository(client = get(named(AUTH_CLIENT)), authUrl = get(named(CURRENT_AUTH_URL))) }
    single { RemoteTokenRepository(client = get(named(AUTH_CLIENT)), authUrl = get(named(CURRENT_AUTH_URL))) }
}
