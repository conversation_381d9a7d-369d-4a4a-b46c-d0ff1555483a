package com.forthosewho.app.login.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.login.analytics.UserOnboardingSignupEvents.SignupScreen
import com.forthosewho.app.login.analytics.UserOnboardingSignupEvents.SignupStarted
import com.forthosewho.app.login.domain.usecase.SignupUseCase
import com.forthosewho.app.login.domain.usecase.ValidateEmailUseCase
import com.forthosewho.app.login.domain.usecase.ValidatePasswordUseCase
import com.forthosewho.app.login.ui.model.RegisterState
import com.forthosewho.app.login.ui.model.RegisterState.registerInfo
import com.forthosewho.app.platform.utils.EnvironmentSelection
import com.forthosewho.app.platform.utils.empty
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class RegisterViewmodel(
    private val signupUseCase: SignupUseCase,
    private val analyticsUseCase: AnalyticsUseCase,
    private val validateEmailUseCase: ValidateEmailUseCase = ValidateEmailUseCase(),
    private val validatePasswordUseCase: ValidatePasswordUseCase = ValidatePasswordUseCase(),
) : ViewModel() {

    private val _state = MutableStateFlow(RegisterState.State.createInitialState())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        checkVisibilityOfBatcave()
        analyticsUseCase.trackEvent(event = SignupScreen())
        if (registerInfo.hasStoredValues()) {
            _state.update { state ->
                state.copy(
                    screenState = RegisterState.ScreenState.StateRegister,
                    formState = RegisterState.FormState.StateFormPrefilled,
                    infoState = RegisterState.RegisterInfo(name = registerInfo.name, email = registerInfo.email)
                )
            }
        } else {
            _state.update { state ->
                state.copy(
                    screenState = RegisterState.ScreenState.StateRegister,
                    formState = RegisterState.FormState.StateFormEmpty,
                    infoState = RegisterState.RegisterInfo()
                )
            }
        }
    }

    private fun checkVisibilityOfBatcave() {
        viewModelScope.launch {
            val isDebug = EnvironmentSelection.isDebug
            _state.update { state -> state.copy(isButtonEnabled = isDebug) }
        }
    }

    fun onEvent(state: RegisterState.State) {
        if (state.screenState is RegisterState.ScreenState.StateRegister) {
            when (state.formState) {
                is RegisterState.FormState.StateFormEmpty -> {
                    _state.update {
                        state.copy(infoState = RegisterState.RegisterInfo())
                    }
                }

                is RegisterState.FormState.StateNameChanged -> {
                    _state.update {
                        state.copy(infoState = _state.value.infoState.copy(name = state.infoState.name))
                    }
                }

                is RegisterState.FormState.StateEmailChanged -> {
                    _state.update {
                        state.copy(infoState = _state.value.infoState.copy(email = state.infoState.email, emailError = null))
                    }
                }

                is RegisterState.FormState.StatePasswordChanged -> {
                    _state.update {
                        state.copy(
                            infoState = _state.value.infoState.copy(
                                password = state.infoState.password,
                                passwordError = null
                            )
                        )
                    }
                }

                is RegisterState.FormState.StateVisiblePassword -> {
                    _state.update {
                        state.copy(infoState = _state.value.infoState.copy(isVisiblePassword = state.infoState.isVisiblePassword))
                    }
                }

                is RegisterState.FormState.StateFormPrefilled -> {
                    _state.update {
                        state.copy(
                            infoState = _state.value.infoState.copy(
                                name = state.infoState.name,
                                email = state.infoState.email,
                                password = String.empty(),
                                isVisiblePassword = false,
                                emailError = null,
                                passwordError = null,
                            )
                        )
                    }
                }
            }
        }
    }

    private fun validateEmail(email: String): Boolean {
        val emailResult = validateEmailUseCase(ValidateEmailUseCase.Params(email = email))
        _state.update { state -> state.copy(infoState = _state.value.infoState.copy(emailError = emailResult.errorResource)) }
        return emailResult.successful
    }

    private fun validatePassword(password: String): Boolean {
        val passwordResult = validatePasswordUseCase(ValidatePasswordUseCase.Params(password = password))
        _state.update { state -> state.copy(infoState = _state.value.infoState.copy(passwordError = passwordResult.errorResource)) }
        return passwordResult.successful
    }

    private fun executeSignup(name: String, email: String, password: String) {
        analyticsUseCase.trackEvent(SignupStarted(properties = mapOf("user_email" to email, "user_name" to name)))
        viewModelScope.launch {
            _state.update { state -> state.copy(screenState = RegisterState.ScreenState.StateLoading) }
            signupUseCase.invoke(SignupUseCase.Params(name = name, username = email, password = password))
                .onSuccess { signupData ->
                    registerInfo.name = name
                    registerInfo.email = email
                    registerInfo.password = password
                    _state.update { state -> state.copy(screenState = RegisterState.ScreenState.StateRegisterSuccess) }
                }.onFailure { error ->
                    val errorDetails = error.details?.errors?.get(0) ?: String.empty()

                    _state.update { state ->
                        val passwordError = validatePasswordUseCase(
                            ValidatePasswordUseCase.Params(
                                password = "",
                                serverError = errorDetails
                            )
                        )
                        state.copy(
                            screenState = RegisterState.ScreenState.StateRegisterFailure,
                            infoState = _state.value.infoState.copy(
                                passwordError = passwordError.errorResource,
                                passwordServerError = passwordError.errorMessage
                            )
                        )
                    }
                }
        }
    }

    fun onAlreadyAUserButtonClicked() {
        _state.update { state -> state.copy(screenState = RegisterState.ScreenState.StateNavigateToLogin) }
    }

    fun onSignUpButtonClicked(name: String, email: String, password: String) {
        if (validateEmail(email) && validatePassword(password)) {
            executeSignup(
                name = name,
                email = email,
                password = password
            )
        }
    }
}
