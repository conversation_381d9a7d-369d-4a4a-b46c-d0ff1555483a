package com.forthosewho.app.login.data.remote

import com.forthosewho.app.login.data.entity.MeResponseRaw
import com.forthosewho.app.login.domain.MeRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Me
import com.forthosewho.app.login.domain.model.Me.Companion.emptyMe
import com.forthosewho.app.network.createUrl
import com.forthosewho.app.network.getResults
import io.ktor.client.HttpClient
import io.ktor.client.request.headers
import io.ktor.http.HttpMethod
import com.github.michaelbull.result.Result

private const val ME_API = "/api/users/me"
private const val AUTHORIZATION = "Authorization"
private const val BEARER = "Bearer "

class RemoteMeRepository(private val client: HttpClient, private val authUrl: String) : MeRepository {

    override suspend fun getMe(
        token: String
    ): Result<Me, ErrorResponse> = client.getResults<MeResponseRaw, Me>(
        requestBlock = {
            createUrl(host = authUrl, path = ME_API) {
                headers { append(AUTHORIZATION, BEARER + token) }
                method = HttpMethod.Get
            }
        },
        onError = { errorMessage -> errorMessage },
        onSuccess = { meRaw -> meRaw?.toMe() ?: emptyMe },
    )
}


