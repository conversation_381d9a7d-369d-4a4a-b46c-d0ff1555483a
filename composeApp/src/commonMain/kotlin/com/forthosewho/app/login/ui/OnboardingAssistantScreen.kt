package com.forthosewho.app.login.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.composables.core.SheetDetent.Companion.FullyExpanded
import com.composables.core.SheetDetent.Companion.Hidden
import com.composables.core.rememberModalBottomSheetState
import com.forthosewho.app.assistant.ui.AssistantBottomSheetDialog
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.login.ui.model.OnboardingAssistantState
import com.forthosewho.app.login.ui.viewmodel.OnboardingAssistantViewModel
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.transitions.tweenFtwDefault
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Preview
@Composable
fun OnboardingAssistantScreen(
    viewModel: OnboardingAssistantViewModel = koinViewModel<OnboardingAssistantViewModel>(),
    onStoriesScreenNavigated: () -> Unit,
) {
    val state by viewModel.state.collectAsState()

    val assistantModalSheetState = rememberModalBottomSheetState(
        animationSpec = tweenFtwDefault(),
        initialDetent = FullyExpanded,
        detents = listOf(Hidden, FullyExpanded)
    )
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        contentWindowInsets = WindowInsets.ime
    ) { paddingValues ->
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.padding(bottom = paddingValues.calculateBottomPadding())
                .background(Blue)
                .fillMaxSize()
                .systemBarsPadding(),
        ) {
            AssistantBottomSheetDialog(
                category = InitializeThreadCategory.ONBOARDING,
                modalSheetState = assistantModalSheetState,
                onDismissRequest = { viewModel.closeAssistantAndProceed() },
            )
        }
        when (state.screenState) {
            is OnboardingAssistantState.ScreenState.StateNavigateToStoriesScreen -> {
                onStoriesScreenNavigated()
            }

            is OnboardingAssistantState.ScreenState.StateInitial -> {
                viewModel.onStateInitial()
            }

            is OnboardingAssistantState.ScreenState.StateOpenAssistant -> {
                assistantModalSheetState.currentDetent = FullyExpanded
            }
        }
    }
}