package com.forthosewho.app.login.data.remote

import com.forthosewho.app.login.data.entity.TokenRaw
import com.forthosewho.app.login.domain.TokenRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Token
import com.forthosewho.app.network.data.entity.ResponseRaw
import com.forthosewho.app.network.domain.HTTPS
import com.github.michaelbull.result.Result
import com.forthosewho.app.network.submitFormRequest
import io.ktor.client.HttpClient
import io.ktor.http.Parameters

internal const val TOKEN_API = "/api/users/token"
internal const val REFRESH_TOKEN = "refresh_token"

class RemoteTokenRepository(private val client: HttpClient, private val authUrl: String) : TokenRepository {

    override suspend fun issueToken(refreshToken: String): Result<Token, ErrorResponse> {
        return client.submitFormRequest<ResponseRaw<TokenRaw>, Token>(
            url = "$HTTPS://$authUrl$TOKEN_API",
            formParameters = Parameters.build { append(REFRESH_TOKEN, refreshToken) },
            onSuccess = { tokenRaw -> tokenRaw?.data?.toToken() ?: Token.emptyToken },
            onError = { errorMessage -> errorMessage }
        )
    }
}
