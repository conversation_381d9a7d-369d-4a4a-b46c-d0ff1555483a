package com.forthosewho.app.login.domain.usecase

import com.forthosewho.app.login.data.remote.RemoteVerifyRepository
import com.forthosewho.app.login.domain.model.ErrorResponse
import com.forthosewho.app.login.domain.model.Verify
import com.github.michaelbull.result.Result

class VerifyUseCase(private val remoteVerifyRepository: RemoteVerifyRepository) {
    suspend operator fun invoke(params: Params): Result<Verify,ErrorResponse> {
        return remoteVerifyRepository.postVerify(
            username = params.username,
            verificationCode = params.verificationCode
        )
    }

    data class Params(
        val username: String,
        val verificationCode: String,
    )
}
