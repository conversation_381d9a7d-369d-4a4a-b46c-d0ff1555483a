package com.forthosewho.app.login.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.forthosewho.app.login.ui.model.VerifyState
import com.forthosewho.app.login.ui.viewmodel.VerifyViewModel
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.theme.buttons.MainClickToActionButton
import com.forthosewho.app.theme.colors.Black
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Cyan
import com.forthosewho.app.theme.colors.Red
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.icon.FtwIcons.Confetti
import com.forthosewho.app.theme.textfields.OtpTextField
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.edit_email
import forthosewho_app.composeapp.generated.resources.hoorey_account_verified
import forthosewho_app.composeapp.generated.resources.verify_your_account
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Preview
@Composable
fun VerifyCodeScreen(
    viewModel: VerifyViewModel = koinViewModel<VerifyViewModel>(),
    onAssistantOnboardingNavigation: () -> Unit = {},
    onBatcaveScreenNavigated: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val state by viewModel.state.collectAsState()
    FTWLoggedOutContent(
        content = {
            when (state.screenState) {
                VerifyState.ScreenState.StateInitial -> {
                    OtpFieldContent(
                        sentEmailTo = state.email,
                        onBackPressed = onBackPressed,
                        onResendClicked = { viewModel.onResendEmailClicked() },
                        onOtpTextChange = { viewModel.onOtpTextChange(it) },
                        onFocusChange = { viewModel.onFocusChange(it) }
                    )
                }

                VerifyState.ScreenState.StateResendCode,
                VerifyState.ScreenState.StateLoading -> {
                    OtpFieldContent(
                        sentEmailTo = state.email,
                        onBackPressed = onBackPressed,
                        onResendClicked = { viewModel.onResendEmailClicked() },
                        onOtpTextChange = { viewModel.onOtpTextChange(it) },
                        onFocusChange = { viewModel.onFocusChange(it) },
                        isLoading = true
                    )
                }

                VerifyState.ScreenState.StateVerificationSuccess -> {
                    SuccessfulOtpContent()
                    LaunchedEffect(Unit) {
                        delay(3000L)
                        viewModel.onAssistantOnboarding()
                    }
                }

                is VerifyState.ScreenState.StateVerificationFailed -> {
                    OtpFieldContent(
                        sentEmailTo = state.email,
                        onBackPressed = onBackPressed,
                        onResendClicked = { viewModel.onResendEmailClicked() },
                        onOtpTextChange = { viewModel.onOtpTextChange(it) },
                        onFocusChange = { viewModel.onFocusChange(it) },
                        isOptFalse = true,
                        errorMessage = (state.screenState as VerifyState.ScreenState.StateVerificationFailed).message,
                    )
                }

                is VerifyState.ScreenState.StateNavigateToAssistantOnboarding -> {
                    onAssistantOnboardingNavigation()
                }
            }
        },
        isButtonClickable = state.isButtonEnabled,
        onBatcaveScreenNavigated = onBatcaveScreenNavigated
    )
}

@Composable
fun OtpFieldContent(
    sentEmailTo: String,
    onBackPressed: () -> Unit,
    onResendClicked: () -> Unit,
    onOtpTextChange: (String) -> Unit,
    onFocusChange: (Boolean) -> Unit,
    errorMessage: String? = null,
    isOptFalse: Boolean = false,
    isLoading: Boolean = false,
) {
    var otpValue by remember { mutableStateOf(String.empty()) }

    Column(
        modifier = Modifier
            .padding(vertical = 40.dp)
            .background(White)
            .fillMaxSize()
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.fillMaxWidth().height(20.dp))
        Text(
            text = stringResource(Res.string.verify_your_account),
            textAlign = TextAlign.Center,
            color = Black,
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
        )
        Spacer(modifier = Modifier.fillMaxWidth().height(16.dp))
        Text(
            text = "We’ve sent an email to $sentEmailTo with an activation code",
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.W500, lineHeight = 25.sp),
            color = Black,
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
        )
        Spacer(modifier = Modifier.fillMaxWidth().height(24.dp))
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(60.dp),
                strokeWidth = 5.dp,
                color = Blue
            )
        } else {
            OtpTextField(
                modifier = Modifier,
                otpText = otpValue,
                isFalse = isOptFalse,
                onOtpTextChange = { value, otpInputFilled ->
                    otpValue = value
                    onOtpTextChange(otpValue)
                },
                onFocusChange = {
                    onFocusChange(it)
                }
            )
        }
        Spacer(modifier = Modifier.fillMaxWidth().height(32.dp))
        if (isOptFalse) {
            Text(
                text = errorMessage ?: "Wrong code please try again",
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = Red,
                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            )
            Spacer(modifier = Modifier.fillMaxWidth().height(32.dp))
        }
        Row {
            MainClickToActionButton(
                buttonText = stringResource(Res.string.edit_email),
                modifier = Modifier.weight(1f).wrapContentHeight(),
                colors = ButtonColors(
                    containerColor = Cyan,
                    contentColor = Blue,
                    disabledContainerColor = Cyan,
                    disabledContentColor = Blue,
                ),
                buttonTextColor = Blue,
                onButtonClick = { onBackPressed() }
            )
//            Spacer(modifier = Modifier.width(16.dp))
//            MainClickToActionButton(
//                buttonText = stringResource(Res.string.resend_code),
//                modifier = Modifier.weight(1f).wrapContentHeight(),
//                colors = ButtonColors(
//                    containerColor = Cyan,
//                    contentColor = Blue,
//                    disabledContainerColor = Cyan,
//                    disabledContentColor = Blue,
//                ),
//                buttonTextColor = Blue,
//                onButtonClick = { onResendClicked() }
//            )
        }
    }
}

@Composable
fun SuccessfulOtpContent() {
    Column(
        modifier = Modifier
            .padding(vertical = 40.dp)
            .background(White)
            .fillMaxSize()
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.fillMaxWidth().height(20.dp))
        Box(
            modifier = Modifier
                .size(70.dp)
                .clip(CircleShape)
                .background(Cyan),
            contentAlignment = Alignment.Center,
        ) {
            Icon(
                imageVector = Confetti,
                contentDescription = null,
                tint = Blue,
                modifier = Modifier.align(Alignment.Center).size(26.dp)
            )
        }
        Spacer(modifier = Modifier.fillMaxWidth().height(10.dp))
        Text(
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            text = stringResource(Res.string.hoorey_account_verified),
            textAlign = TextAlign.Center,
            color = Black,
            style = MaterialTheme.typography.titleMedium.copy(
                fontSize = 22.sp,
                lineHeight = 27.sp
            ),
        )
        Spacer(modifier = Modifier.fillMaxWidth().height(16.dp))
    }
}