package com.forthosewho.app.login.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.forthosewho.app.login.ui.model.Intro
import com.forthosewho.app.login.ui.viewmodel.LoginViewModel
import com.forthosewho.app.theme.colors.White
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.logo
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Preview
@Composable
fun LoginScreen(
    loginViewModel: LoginViewModel = koinViewModel<LoginViewModel>(),
    onLoginInfoScreenNavigated: () -> Unit,
    onStoriesScreenNavigated: () -> Unit,
    onAssistantOnboardingScreenNavigated: () -> Unit,
) {
    val state by loginViewModel.state.collectAsState()

    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .background(White)
            .fillMaxSize()
            .systemBarsPadding(),
    ) {
        Image(
            modifier = Modifier.padding(bottom = 100.dp),
            painter = painterResource(Res.drawable.logo),
            contentDescription = null
        )
    }
    when (state.screenState) {
        is Intro.ScreenState.StateInitial -> {
            loginViewModel.onStateInitial()
        }

        is Intro.ScreenState.StateLoggedIn -> {
            onStoriesScreenNavigated()
        }

        is Intro.ScreenState.StateLoggedOut -> {
            onLoginInfoScreenNavigated()
        }

        is Intro.ScreenState.StateAssistantOnboarding -> {
            onAssistantOnboardingScreenNavigated()
        }
    }
}