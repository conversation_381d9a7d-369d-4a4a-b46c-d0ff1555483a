package com.forthosewho.app.login.data.entity

import com.forthosewho.app.login.domain.model.Token
import kotlinx.serialization.Serializable

@Serializable
data class TokenRaw(
    private val accessToken: String,
    private val accessTokenExpiresAt: String,
    private val refreshToken: String,
    private val refreshTokenExpiresAt: String
) {
    fun toToken() = Token(
        accessToken = accessToken,
        accessTokenExpiresAt = accessTokenExpiresAt,
        refreshToken = refreshToken,
        refreshTokenExpiresAt = refreshTokenExpiresAt,
    )
}


