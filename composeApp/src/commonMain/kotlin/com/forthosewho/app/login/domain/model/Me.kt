package com.forthosewho.app.login.domain.model

import com.forthosewho.app.platform.utils.empty

data class Me(
    val username: String,
    val userId: String,
    val fullName: String,
    val status: Status,
) {

    companion object {

        val emptyMe = Me(
            username = String.empty(),
            userId = String.empty(),
            fullName = String.empty(),
            status = Status.NOT_ONBOARDED
        )
    }

    enum class Status(val status: String) {
        INVITED("invited"),
        VERIFIED("verified"),
        ONBOARDED("onboarded"),
        NOT_ONBOARDED("not_onboarded");

        companion object {
            fun from(status: String?): Status =
                entries.firstOrNull { it.status.lowercase() == status?.lowercase() } ?: NOT_ONBOARDED
        }
    }
}
