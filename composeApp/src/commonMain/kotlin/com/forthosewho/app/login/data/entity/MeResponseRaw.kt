package com.forthosewho.app.login.data.entity

import com.forthosewho.app.login.domain.model.Me
import com.forthosewho.app.login.domain.model.Me.Status.Companion.from
import com.forthosewho.app.platform.utils.empty
import kotlinx.serialization.Serializable

@Serializable
data class MeResponseRaw(
    private val username: String? = null,
    private val id: String? = null,
    private val metadata: Metadata? = null,
    private val status: String? = null
) {

    fun toMe() = Me(
        username = username.toString(),
        userId = id.toString(),
        fullName = metadata?.toName() ?: String.empty(),
        status = from(status)
    )
}

@Serializable
data class Metadata(
    private val name: String? = null,
) {
    fun toName(): String? = name
}
