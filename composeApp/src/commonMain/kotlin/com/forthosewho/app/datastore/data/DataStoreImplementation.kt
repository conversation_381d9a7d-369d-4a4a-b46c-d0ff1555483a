package com.forthosewho.app.datastore.data

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.createDataStore
import com.forthosewho.app.datastore.domain.DataStorage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map

class DataStoreImplementation(private val dataStore: DataStore<Preferences> = createDataStore()) : DataStorage {

    override suspend fun write(key: DataStoreValues, value: Any) {
        when (value) {
            is String -> dataStore.edit { preferences -> preferences[stringPreferencesKey(key.name)] = value }
            is Int -> dataStore.edit { preferences -> preferences[intPreferencesKey(key.name)] = value }
            is Long -> dataStore.edit { preferences -> preferences[longPreferencesKey(key.name)] = value }
            is Boolean -> dataStore.edit { preferences -> preferences[booleanPreferencesKey(key.name)] = value }
            else -> throw IllegalArgumentException("Unsupported data type")
        }
    }

    override fun <T> read(key: DataStoreValues, defaultVal: T): Flow<Comparable<*>> {
        return when (defaultVal) {
            is String -> readValue(stringPreferencesKey(key.name), defaultVal)
            is Int -> readValue(intPreferencesKey(key.name), defaultVal)
            is Long -> readValue(longPreferencesKey(key.name), defaultVal)
            is Boolean -> readValue(booleanPreferencesKey(key.name), defaultVal)
            else -> throw IllegalArgumentException("Unsupported data type")
        }
    }

    override suspend fun clearAll() {
        dataStore.edit { preferences ->
            val keepValue = preferences[booleanPreferencesKey(DataStoreValues.IS_STAGING.name)] // Get value of key to keep
            preferences.clear() // Clear all preferences
            if (keepValue != null) {
                preferences[booleanPreferencesKey(DataStoreValues.IS_STAGING.name)] = keepValue // Restore the kept key
            }
        }
    }

    private inline fun <reified T> readValue(key: Preferences.Key<T>, defaultVal: T): Flow<T> {
        return dataStore.data.map { preferences -> preferences[key] ?: defaultVal }.catch { e -> emit(defaultVal) }
    }
}
