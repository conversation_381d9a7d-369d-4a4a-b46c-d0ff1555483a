package com.forthosewho.app.datastore.domain.usecase

import com.forthosewho.app.datastore.domain.DataStorage
import com.forthosewho.app.datastore.data.DataStoreImplementation
import com.forthosewho.app.datastore.DataStoreValues

class SetStoredValueUseCase(private val dataStorage: DataStorage = DataStoreImplementation()) {
    suspend operator fun invoke(params: Params) {
        if (params.value !is String && params.value !is Int && params.value !is Long && params.value !is Boolean) {
            throw IllegalArgumentException("Unsupported data type")
        }
        return dataStorage.write(params.dataStoreValue, params.value)
    }

    data class Params(
        val dataStoreValue: DataStoreValues,
        val value: Any
    )
}
