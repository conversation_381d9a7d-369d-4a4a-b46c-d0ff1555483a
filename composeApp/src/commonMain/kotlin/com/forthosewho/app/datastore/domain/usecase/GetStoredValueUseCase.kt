package com.forthosewho.app.datastore.domain.usecase

import com.forthosewho.app.datastore.domain.DataStorage
import com.forthosewho.app.datastore.data.DataStoreImplementation
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.platform.utils.empty
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first

class GetStoredValueUseCase(private val dataStorage: DataStorage = DataStoreImplementation()) {

    operator fun invoke(params: Params): Flow<Any> = dataStorage.read(params.dataStoreValue, params.defaultVal)

    suspend fun getString(key: DataStoreValues, defaultVal: String = String.empty()): String {
        return invoke(Params(dataStoreValue = key, defaultVal = defaultVal)).first().toString()
    }

    suspend fun getBoolean(key: DataStoreValues, defaultVal: Boolean = false): Boolean {
        return invoke(Params(dataStoreValue = key, defaultVal = defaultVal)).first().toString().toBoolean()
    }

    data class Params(
        val dataStoreValue: DataStoreValues,
        val defaultVal: Any,
    )
}