package com.forthosewho.app.datastore

enum class DataStoreValues(val key: String, val debug: Boolean = false) {
    EMAIL("email"),
    PASSWORD("password"),
    LOGGED_IN("logged_in"),
    ACCESS_TOKEN("access_token"),
    REFRESH_TOKEN("refresh_token"),
    USER_ID("user_id"),
    NAME("name"),
    CLUES("clues"),
    FIRST_ONBOARDING("first_onboarding"),

    //------------ DEBUG ONLY (set debug to true) ------------------//
    IS_STAGING("is_staging", debug = true),
}
