package com.forthosewho.app.analytics.domain.usecase

import com.forthosewho.app.analytics.Analytics
import com.forthosewho.app.analytics.AnalyticsEvent

class AnalyticsUseCase(private val analyticsManager: Analytics) {

    fun initialize(token: String) {
        analyticsManager.initialize(token)
    }

    fun trackEvent(event: AnalyticsEvent) {
        analyticsManager.track(event)
    }

    fun identify(userID: String) {
        analyticsManager.identify(userID)
    }
}