package com.forthosewho.app.navigation

import androidx.compose.ui.graphics.vector.ImageVector
import com.forthosewho.app.theme.icon.FtwIcons

sealed class BottomNavigationItems(
    val route: String,
    val title: String,
    val icon: ImageVector,
) {
    object Stories : BottomNavigationItems(
        route = Routes.StoriesScreen.route,
        title = "Stories",
        icon = FtwIcons.Stories
    )

    object Settings : BottomNavigationItems(
        route = Routes.SettingsScreen.route,
        title = "Settings",
        icon = FtwIcons.AccountCircle
    )
}