package com.forthosewho.app.navigation

sealed class Routes(val route: String) {
    object OnboardingFirstExperienceScreen : Routes(route = "onboardingFirstExperienceScreen")
    object OnboardingAssistantScreen : Routes(route = "onboardingAssistant")
    object SettingsScreen : Routes(route = "settings")
    object LoginInfoScreen : Routes(route = "loginInfo")
    object RegisterInfoScreen : Routes(route = "register")
    object VerifyCodeScreen : Routes(route = "verifyCode")
    object LoginScreen : Routes(route = "login")
    object StoriesScreen : Routes(route = "stories")
    object StoriesForClueScreen : Routes(route = "storiesForClue")
    object BatcaveScreen : Routes(route = "batcave")
}
