package com.forthosewho.app.navigation

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.forthosewho.app.batcave.ui.BatcaveScreen
import com.forthosewho.app.login.ui.LoginInfoScreen
import com.forthosewho.app.login.ui.LoginScreen
import com.forthosewho.app.login.ui.OnboardingAssistantScreen
import com.forthosewho.app.login.ui.RegisterScreen
import com.forthosewho.app.login.ui.VerifyCodeScreen
import com.forthosewho.app.onboarding.ui.OnboardingFirstExperienceScreen
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.profile_home.ProfileScreen
import com.forthosewho.app.stories_home.ui.CLUE_ID
import com.forthosewho.app.stories_home.ui.StoriesForClueListScreen
import com.forthosewho.app.stories_home.ui.StoryListScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FtwNavigationGraph(
    navController: NavHostController,
    startDestination: String = Routes.LoginScreen.route,
    onTopBarVisibilityChanged: (Boolean) -> Unit,
    onBottomBarVisibilityChanged: (Boolean) -> Unit
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        composable(route = Routes.BatcaveScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            BatcaveScreen(
                onBackPressed = { navController.currentBackStackEntry?.let { navController.popBackStack() } },
                onNavigateToStart = { navController.navigate(Routes.LoginScreen.route) }
            )
        }
        composable(route = Routes.LoginScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            LoginScreen(
                onLoginInfoScreenNavigated = { navController.navigate(Routes.RegisterInfoScreen.route) },
                onStoriesScreenNavigated = { navController.navigate(Routes.StoriesScreen.route) },
                onAssistantOnboardingScreenNavigated = { navController.navigate(Routes.OnboardingFirstExperienceScreen.route) }
            )
        }
        composable(route = Routes.LoginInfoScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            LoginInfoScreen(
                onBackPressed = { navController.currentBackStackEntry?.let { navController.popBackStack() } },
                onStoriesScreenNavigated = { navController.navigate(Routes.StoriesScreen.route) },
                onBatcaveScreenNavigated = { navController.navigate(Routes.BatcaveScreen.route) },
                onAssistantOnboardingScreenNavigated = { navController.navigate(Routes.OnboardingFirstExperienceScreen.route) }
            )
        }
        composable(route = Routes.RegisterInfoScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            RegisterScreen(
                onAlreadyAUserButtonNavigated = { navController.navigate(Routes.LoginInfoScreen.route) },
                onVerifyCodeScreenNavigated = { navController.navigate(Routes.VerifyCodeScreen.route) },
                onBatcaveScreenNavigated = { navController.navigate(Routes.BatcaveScreen.route) },
                onBackPressed = { navController.popBackStack() }
            )
        }
        composable(route = Routes.VerifyCodeScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            VerifyCodeScreen(
                onBackPressed = { navController.currentBackStackEntry?.let { navController.popBackStack() } },
                onBatcaveScreenNavigated = { navController.navigate(Routes.BatcaveScreen.route) },
                onAssistantOnboardingNavigation = { navController.navigate(Routes.OnboardingFirstExperienceScreen.route) }
            )
        }
        composable(route = Routes.OnboardingFirstExperienceScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            OnboardingFirstExperienceScreen(
                onAssistantOnboardingNavigation = { navController.navigate(Routes.OnboardingAssistantScreen.route) },
            )
        }
        composable(route = Routes.OnboardingAssistantScreen.route) {
            onTopBarVisibilityChanged(false)
            onBottomBarVisibilityChanged(false)
            OnboardingAssistantScreen(
                onStoriesScreenNavigated = { navController.navigate(Routes.StoriesScreen.route) },
            )
        }
        composable(route = Routes.StoriesScreen.route) {
            onTopBarVisibilityChanged(true)
            onBottomBarVisibilityChanged(true)
            StoryListScreen(
                onStoriesForCluesScreenNavigated = { navController.navigate(Routes.StoriesForClueScreen.route + "/$it") },
                onLogout = { navController.navigate(Routes.LoginScreen.route) },
                onBackPressed = { navController.popBackStack() },
            )
        }
        composable(route = Routes.SettingsScreen.route) {
            onTopBarVisibilityChanged(true)
            onBottomBarVisibilityChanged(true)
            ProfileScreen(
                onBackPressed = { navController.popBackStack() },
                onNavigateToStart = { navController.navigate(Routes.LoginScreen.route) }
            )
        }
        composable(
            route = Routes.StoriesForClueScreen.route + "/{$CLUE_ID}",
            arguments = listOf(navArgument(CLUE_ID) {
                defaultValue = String.empty()
                type = NavType.StringType
            })
        ) {
            onTopBarVisibilityChanged(true)
            onBottomBarVisibilityChanged(true)
            StoriesForClueListScreen(
                onBackPressed = { navController.popBackStack() },
                onLogout = { navController.navigate(Routes.LoginScreen.route) },
            )
        }
    }
}
