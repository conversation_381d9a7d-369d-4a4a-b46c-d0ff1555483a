package com.forthosewho.app.profile_home

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.displayCutoutPadding
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.composables.core.ModalBottomSheet
import com.composables.core.ModalBottomSheetState
import com.composables.core.ModalSheetProperties
import com.composables.core.Scrim
import com.composables.core.Sheet
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.theme.buttons.MainClickToActionButton
import com.forthosewho.app.theme.cards.ClueComposable
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Cyan
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.Red
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.icon.FtwIcons.Pencil
import com.forthosewho.app.theme.icon.FtwIcons.Trash
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.are_you_sure_you_want_to_delete_this_clue
import forthosewho_app.composeapp.generated.resources.cancel
import forthosewho_app.composeapp.generated.resources.delete
import forthosewho_app.composeapp.generated.resources.edit
import forthosewho_app.composeapp.generated.resources.what_do_you_want_to_do
import org.jetbrains.compose.resources.stringResource

@Composable
fun ClueActionConfirmationBottomSheet(
    modifier: Modifier,
    modalSheetState: ModalBottomSheetState,
    clue: Clue,
    onEditPressed: () -> Unit,
    onDeletePressed: () -> Unit,
    onDismissRequest: () -> Unit
) {
    ModalBottomSheet(
        state = modalSheetState,
        onDismiss = { onDismissRequest() },
        properties = ModalSheetProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        ),
    ) {
        Scrim(
            scrimColor = Color.Black.copy(0.3f),
            enter = fadeIn(), exit = fadeOut()
        )
        Sheet(
            modifier = modifier
                .displayCutoutPadding()
                .statusBarsPadding()
                .padding(
                    WindowInsets.navigationBars.only(WindowInsetsSides.Horizontal).asPaddingValues()
                )
                .shadow(4.dp, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .background(White)
                .widthIn(max = 640.dp)
                .fillMaxWidth()
                .imePadding(),
        ) {
            Content(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal = 20.dp, vertical = 20.dp),
                toolbarContent = {
                    Text(
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        text = stringResource(Res.string.what_do_you_want_to_do),
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleMedium
                    )
                },
                clueContent = {
                    ClueComposable(
                        modifier = Modifier.padding(vertical = 8.dp),
                        text = clue.clue,
                    )
                },
                ctaTopContent = {
                    MainClickToActionButton(
                        buttonText = stringResource(Res.string.edit),
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        colors = ButtonColors(
                            containerColor = Cyan,
                            contentColor = Blue,
                            disabledContainerColor = Cyan,
                            disabledContentColor = Blue,
                        ),
                        icon = Pencil,
                        buttonTextColor = Blue,
                        onButtonClick = { onEditPressed() }
                    )
                },
                ctaBottomContent = {
                    MainClickToActionButton(
                        buttonText = stringResource(Res.string.delete),
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        colors = ButtonColors(
                            containerColor = Red,
                            contentColor = White,
                            disabledContainerColor = Red,
                            disabledContentColor = White,
                        ),
                        icon = Trash,
                        buttonTextColor = White,
                        onButtonClick = { onDeletePressed() }
                    )
                },
            )
        }
    }
}

@Composable
fun ClueDeletionConfirmationBottomSheet(
    modifier: Modifier,
    modalSheetState: ModalBottomSheetState,
    clue: Clue,
    onDeleteConfirmationPressed: () -> Unit,
    onDismissRequest: () -> Unit,
) {
    ModalBottomSheet(
        state = modalSheetState,
        onDismiss = { onDismissRequest() },
        properties = ModalSheetProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        ),
    ) {
        Scrim(scrimColor = Color.Black.copy(0.3f), enter = fadeIn(), exit = fadeOut())
        Sheet(
            modifier = modifier
                .padding(top = 12.dp)
                .displayCutoutPadding()
                .statusBarsPadding()
                .padding(
                    WindowInsets.navigationBars.only(WindowInsetsSides.Horizontal).asPaddingValues()
                )
                .shadow(4.dp, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                .background(White)
                .widthIn(max = 640.dp)
                .fillMaxWidth()
                .imePadding(),
        ) {
            Content(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal = 20.dp, vertical = 20.dp),
                toolbarContent = {
                    Text(
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        text = stringResource(Res.string.are_you_sure_you_want_to_delete_this_clue),
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleMedium
                    )
                },
                clueContent = {
                    ClueComposable(
                        modifier = Modifier.padding(vertical = 8.dp),
                        text = clue.clue,
                    )
                },
                ctaTopContent = {
                    var loadingVisible by remember { mutableStateOf(false) }
                    MainClickToActionButton(
                        buttonText = stringResource(Res.string.delete),
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        colors = ButtonColors(
                            containerColor = Red,
                            contentColor = White,
                            disabledContainerColor = Red,
                            disabledContentColor = White,
                        ),
                        icon = Trash,
                        isLoading = loadingVisible,
                        buttonTextColor = White,
                        onButtonClick = {
                            onDeleteConfirmationPressed()
                            loadingVisible = true
                        }
                    )
                },
                ctaBottomContent = {
                    MainClickToActionButton(
                        buttonText = stringResource(Res.string.cancel),
                        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                        colors = ButtonColors(
                            containerColor = Beige,
                            contentColor = GreyLight,
                            disabledContainerColor = Beige,
                            disabledContentColor = Beige,
                        ),
                        buttonTextColor = GreyLight,
                        onButtonClick = {
                            onDismissRequest()
                        }
                    )
                },
            )
        }
    }
}

@Composable
private fun Content(
    modifier: Modifier = Modifier,
    toolbarContent: @Composable () -> Unit = {},
    clueContent: @Composable () -> Unit = {},
    ctaTopContent: @Composable () -> Unit = {},
    ctaBottomContent: @Composable () -> Unit = {},
) {
    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(8.dp))
        toolbarContent()
        Spacer(modifier = Modifier.height(10.dp))
        clueContent()
        Spacer(modifier = Modifier.height(16.dp))
        ctaTopContent()
        Spacer(modifier = Modifier.height(16.dp))
        ctaBottomContent()
        Spacer(modifier = Modifier.height(28.dp))
    }
}
