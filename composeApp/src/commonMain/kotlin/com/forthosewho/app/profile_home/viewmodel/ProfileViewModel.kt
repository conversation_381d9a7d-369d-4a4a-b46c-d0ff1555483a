package com.forthosewho.app.profile_home.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.account.domain.usecase.AccountUseCase
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.assistant.domain.usecase.UserCluesUseCase
import com.forthosewho.app.assistant.ui.model.CluesContainer
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.ClearStoredValuesUseCase
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.login.analytics.UserOnboardingSignupEvents
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.profile_home.analytics.NUM_EXISTING_CLUES
import com.forthosewho.app.profile_home.analytics.ProfileEvents
import com.forthosewho.app.profile_home.model.ProfileState
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

class ProfileViewModel(
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val clearStoredValuesUseCase: ClearStoredValuesUseCase = ClearStoredValuesUseCase(),
    private val cluesUseCase: UserCluesUseCase,
    private val accountUseCase: AccountUseCase,
    private val analyticsUseCase: AnalyticsUseCase,
) : ViewModel() {

    private val _state = MutableStateFlow(ProfileState.State.createInitialState())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        getAccountUseCase()
    }

    fun getAccountUseCase() {
        viewModelScope.launch {
            try {
                //already stored values
                _state.update { state ->
                    state.copy(
                        screenState = ProfileState.ScreenState.Initial,
                        name = getStoredValueUseCase.getString(DataStoreValues.NAME, String.empty()),
                        email = getStoredValueUseCase.getString(DataStoreValues.EMAIL, String.empty()),
                        cluesList = Json.decodeFromString(
                            getStoredValueUseCase.getString(
                                DataStoreValues.CLUES,
                                String.empty()
                            )
                        )
                    )
                }
            } catch (e: Exception) {
            }
            //updated values
            accountUseCase.invoke().onSuccess {
                _state.update { state -> state.copy(cluesList = it.clues) }
            }.onFailure {
                if (it.code == 400) {
                    _state.update { state ->
                        state.copy(screenState = ProfileState.ScreenState.NavigateToLogin)
                    }
                }
            }
            analyticsUseCase.trackEvent(
                ProfileEvents.ProfileViewed(
                    properties = mapOf(NUM_EXISTING_CLUES to _state.value.cluesList.count().toString())
                )
            )
        }
    }

    fun selectClue(clue: Clue?) {
        viewModelScope.launch {
            _state.update { state -> state.copy(currentClue = clue) }
        }
    }

    fun onDeleteConfirmationPressed(clue: Clue) {
        viewModelScope.launch {
            cluesUseCase.invoke(
                UserCluesUseCase.Params(
                    cluesContainer = CluesContainer(cluesToBeRemoved = listOf(clue))
                )
            ).onSuccess {
                _state.update { state ->
                    state.copy(
                        currentClue = null,
                        screenState = ProfileState.ScreenState.Initial,
                        cluesList = state.cluesList.filterNot { it == clue }
                    )
                }
            }.onFailure {
                if (it.code == 401) {
                    _state.update { state ->
                        state.copy(screenState = ProfileState.ScreenState.NavigateToLogin)
                    }
                }
            }
        }
    }

    fun closeAssistant() {
        _state.update { state ->
            state.copy(
                screenState = ProfileState.ScreenState.Initial,
                cluesList = state.cluesList
            )
        }
        //refreshes clues incase clues have changed
        viewModelScope.launch {
            accountUseCase.invoke().onSuccess {
                _state.update { state ->
                    state.copy(
                        cluesList = it.clues
                    )
                }
            }
        }
    }

    fun onSignOutClicked() {
        analyticsUseCase.trackEvent(UserOnboardingSignupEvents.Logout())
        viewModelScope.launch {
            clearStoredValuesUseCase.invoke()
            _state.update { state ->
                state.copy(screenState = ProfileState.ScreenState.NavigateToLogin)
            }
        }
    }
}
