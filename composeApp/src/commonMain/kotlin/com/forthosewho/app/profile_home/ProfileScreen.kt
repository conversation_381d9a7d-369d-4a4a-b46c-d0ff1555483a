@file:OptIn(ExperimentalMaterial3Api::class)

package com.forthosewho.app.profile_home

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.unit.dp
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.assistant.ui.AssistantBottomSheetDialog
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.profile_home.model.ProfileState
import com.forthosewho.app.profile_home.viewmodel.ProfileViewModel
import com.forthosewho.app.theme.bottomsheets.rememberSheetController
import com.forthosewho.app.theme.buttons.MainClickToActionButton
import com.forthosewho.app.theme.cards.ClueComposableTappable
import com.forthosewho.app.theme.colors.Beige
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.GreySuperLight
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.icon.FtwIcons
import com.forthosewho.app.theme.icon.FtwIcons.Dots
import com.forthosewho.app.theme.toolbars.StoriesWithSubtitleTopAppBar
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.share_something_new
import forthosewho_app.composeapp.generated.resources.your_clues
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun ProfileScreen(
    viewModel: ProfileViewModel = koinViewModel<ProfileViewModel>(),
    scrollBehavior: TopAppBarScrollBehavior = TopAppBarDefaults.pinnedScrollBehavior(),
    onNavigateToStart: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val state by viewModel.state.collectAsState()

    val editCluesSheetController = rememberSheetController()
    val deleteCluesSheetController = rememberSheetController()
    val assistantSheetController = rememberSheetController()

    LaunchedEffect(state.screenState) {
        when (state.screenState) {
            is ProfileState.ScreenState.NavigateToLogin -> {
                onNavigateToStart()
            }

            is ProfileState.ScreenState.NavigateBack -> {
                onBackPressed()
            }

            is ProfileState.ScreenState.Initial -> {
                viewModel.onStateInitial()
            }
        }
    }

    Scaffold(
        contentColor = Color.Transparent.copy(alpha = 0.5F),
        modifier = Modifier.fillMaxSize().nestedScroll(connection = scrollBehavior.nestedScrollConnection),
        topBar = {
            StoriesWithSubtitleTopAppBar(
                modifier = Modifier.fillMaxWidth().padding(top = 72.dp),
                title = state.name,
                titleStyle = MaterialTheme.typography.titleSmall,
                subtitle = state.email,
                subtitleStyle = MaterialTheme.typography.bodyLarge,
                rightIconButton = FtwIcons.Signout,
                onRightIconClicked = { viewModel.onSignOutClicked() },
            )
        },
        bottomBar = {
            MainClickToActionButton(
                buttonText = stringResource(Res.string.share_something_new),
                modifier = Modifier.fillMaxWidth().wrapContentHeight().padding(bottom = 30.dp, start = 20.dp, end = 20.dp),
                colors = ButtonColors(
                    containerColor = Blue,
                    contentColor = Color.Transparent,
                    disabledContainerColor = GreySuperLight,
                    disabledContentColor = Color.Transparent,
                ),
                buttonTextColor = White,
                onButtonClick = {
                    viewModel.selectClue(clue = null)
                    assistantSheetController.show()
                }
            )
        },
        contentWindowInsets = WindowInsets.systemBars,
    ) { paddingValues ->
        ProfileScreenContent(
            modifier = Modifier.fillMaxSize().padding(
                top = paddingValues.calculateTopPadding(),
                bottom = paddingValues.calculateBottomPadding()
            ),
            cluesList = state.cluesList,
            onClueItemClicked = {
                editCluesSheetController.show()
                viewModel.selectClue(clue = it)
            },
        )
    }

    state.currentClue?.let { clue ->
        ClueActionConfirmationBottomSheet(
            modifier = Modifier,
            modalSheetState = editCluesSheetController.state,
            clue = clue,
            onEditPressed = {
                editCluesSheetController.hide()
                assistantSheetController.show()
            },
            onDeletePressed = {
                editCluesSheetController.hide()
                deleteCluesSheetController.show()
            },
            onDismissRequest = {
                editCluesSheetController.hide()
            },
        )
        ClueDeletionConfirmationBottomSheet(
            modifier = Modifier,
            modalSheetState = deleteCluesSheetController.state,
            clue = clue,
            onDismissRequest = {
                deleteCluesSheetController.hide()
            },
            onDeleteConfirmationPressed = {
                viewModel.onDeleteConfirmationPressed(clue)
            },
        )
        AssistantBottomSheetDialog(
            modalSheetState = assistantSheetController.state,
            category = InitializeThreadCategory.EDIT_CLUE,
            itemId = state.currentClue?.id,
            onDismissRequest = {
                viewModel.closeAssistant()
                assistantSheetController.hide()
            },
        )
    }
    if (state.currentClue == null) {
        AssistantBottomSheetDialog(
            modalSheetState = assistantSheetController.state,
            category = InitializeThreadCategory.CHANGE_CLUES,
            onDismissRequest = {
                viewModel.closeAssistant()
                assistantSheetController.hide()
            },
        )
    }
}


@Composable
fun ProfileScreenContent(
    modifier: Modifier,
    cluesList: List<Clue>,
    onClueItemClicked: ((Clue) -> Unit?),
) {
    CluesBottomList(
        modifier = modifier.fillMaxSize(),
        cluesList = cluesList,
        onClueClicked = { onClueItemClicked(it) },
    )
}

@Composable
fun CluesBottomList(
    modifier: Modifier = Modifier,
    cluesList: List<Clue>,
    onClueClicked: (Clue) -> Unit,
    contentPadding: PaddingValues = PaddingValues(
        start = 20.dp,
        end = 20.dp,
        top = 10.dp,
        bottom = 20.dp
    ),
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = 10.dp),
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
) {
    val scrollableState = rememberLazyListState()

    LazyColumn(
        modifier = modifier,
        contentPadding = contentPadding,
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
        state = scrollableState,
    ) {
        cluesList.forEach { clue ->
            item(key = clue.id) {
                if (cluesList.indexOf(clue) == 0) {
                    Text(
                        modifier = Modifier.padding(bottom = 10.dp),
                        text = stringResource(Res.string.your_clues),
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                }
                ClueComposableTappable(
                    modifier = Modifier,
                    text = clue.clue,
                    contentColor = MaterialTheme.colorScheme.onSurface,
                    onClick = { onClueClicked(clue) },
                    imageVector = Dots,
                    containerColor = Beige,
                    iconButtonColors = IconButtonColors(
                        containerColor = Beige,
                        contentColor = GreyLight,
                        disabledContainerColor = Beige,
                        disabledContentColor = GreyLight,
                    )
                )
            }
        }
    }
}
