package com.forthosewho.app.profile_home.model

import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.platform.utils.empty

class ProfileState {
    data class State(
        val screenState: ScreenState,
        val currentClue: Clue? = null,
        val name: String = String.empty(),
        val email: String = String.empty(),
        val cluesList: List<Clue> = listOf(),
        val assistant: InitializeThreadCategory = InitializeThreadCategory.CHANGE_CLUES,
        val clueId: String? = null
    ) {
        companion object {
            fun createInitialState(): State = State(screenState = ScreenState.Initial)
        }
    }

    sealed class ScreenState {
        data object Initial : ScreenState()
        data object NavigateToLogin : ScreenState()
        data object NavigateBack : ScreenState()
    }
}
