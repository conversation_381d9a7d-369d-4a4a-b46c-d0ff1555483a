package com.forthosewho.app.onboarding.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.snapping.SnapPosition
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.PagerDefaults
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.forthosewho.app.onboarding.ui.model.OnboardingFirstExperienceState
import com.forthosewho.app.onboarding.ui.model.OnboardingFirstExperienceState.getPageContent
import com.forthosewho.app.onboarding.ui.viewmodel.OnboardingFirstExperienceViewModel
import com.forthosewho.app.theme.buttons.RoundedButton
import com.forthosewho.app.theme.colors.Blue
import com.forthosewho.app.theme.colors.Grey
import com.forthosewho.app.theme.colors.GreyLight
import com.forthosewho.app.theme.colors.White
import com.forthosewho.app.theme.icon.FtwIcons.ArrowRight
import com.forthosewho.app.theme.indicators.Indicators
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun OnboardingFirstExperienceScreen(
    viewModel: OnboardingFirstExperienceViewModel = koinViewModel<OnboardingFirstExperienceViewModel>(),
    onAssistantOnboardingNavigation: () -> Unit = {},
) {
    val state by viewModel.state.collectAsState()
    val pagerState = rememberPagerState(
        initialPageOffsetFraction = 0F,
        pageCount = { state.pagesSize },
        initialPage = 0
    )

    when (state.screenState) {
        is OnboardingFirstExperienceState.ScreenState.StateOnboardingEnd -> {
            onAssistantOnboardingNavigation()
        }

        is OnboardingFirstExperienceState.ScreenState.StateOnboarding -> {
            viewModel.onStateInitial()
        }
    }

    OnboardingFirstExperiencePager(
        pageContent = {
            val pageContent = getPageContent(pagerState.currentPage)
            Image(
                modifier = Modifier.fillMaxWidth().padding(20.dp),
                alignment = Alignment.Center,
                painter = painterResource(pageContent.imageRes),
                contentDescription = null
            )
        },
        textContent = {
            val pageContent = getPageContent(pagerState.currentPage)
            Column {
                Text(
                    text = stringResource(pageContent.title),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.displayLarge.copy(fontSize = 34.sp, lineHeight = 36.sp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(top = 24.dp),
                )
                Text(
                    text = stringResource(pageContent.subtitle),
                    textAlign = TextAlign.Start,
                    style = MaterialTheme.typography.bodyLarge.copy(fontSize = 22.sp, lineHeight = 30.sp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(top = 24.dp),
                )
            }
        },
        onStateOnboardingEnded = { viewModel.onStateOnboardingEnded() },
        count = state.pagesSize,
        pagerState = pagerState,
    )
}


@Composable
fun OnboardingFirstExperiencePager(
    pageContent: @Composable () -> Unit = {},
    textContent: @Composable () -> Unit = {},
    count: Int,
    coroutineScope: CoroutineScope = rememberCoroutineScope(),
    pagerState: PagerState,
    onStateOnboardingEnded: () -> Unit = {},
) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        contentWindowInsets = WindowInsets.systemBars
    ) { paddingValues ->
        HorizontalPager(
            state = pagerState,
            pageSize = PageSize.Fill,
            modifier = Modifier.fillMaxWidth().padding(bottom = paddingValues.calculateBottomPadding()),
            verticalAlignment = Alignment.CenterVertically,
            userScrollEnabled = true,
            reverseLayout = false,
            contentPadding = PaddingValues(0.dp),
            flingBehavior = PagerDefaults.flingBehavior(
                state = pagerState,
                snapPositionalThreshold = 0.25f
            ),
            snapPosition = SnapPosition.Center,
            pageSpacing = 0.dp,
        ) { page ->
            Column(
                modifier = Modifier
                    .background(White)
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .background(Blue)
                        .fillMaxWidth()
                        .weight(0.66f),
                ) {
                    pageContent()
                }
                Column(
                    modifier = Modifier
                        .weight(0.35f)
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp)
                ) {
                    textContent()
                }
            }
        }
        Row(
            modifier = Modifier.fillMaxSize().padding(
                bottom = 60.dp,
                start = 24.dp,
                end = 24.dp
            ),
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Indicators(
                count = count,
                size = 10,
                spacer = 5,
                selectedColor = Blue,
                unselectedColor = Grey,
                selectedIndex = pagerState.currentPage,
                selectedLength = 50,
                modifier = Modifier.wrapContentWidth()
            )
            RoundedButton(
                modifier = Modifier.size(36.dp),
                onClick = {
                    coroutineScope.launch {
                        if (pagerState.currentPage == count - 1) {
                            onStateOnboardingEnded()
                        } else {
                            pagerState.animateScrollToPage(
                                page = (pagerState.currentPage + 1).coerceAtMost(count - 1),
                                pageOffsetFraction = 0f,
                            )
                        }
                    }
                },
                colors = IconButtonColors(
                    containerColor = Blue,
                    contentColor = White,
                    disabledContainerColor = GreyLight,
                    disabledContentColor = GreyLight,
                )
            ) {
                Icon(
                    modifier = Modifier.width(36.dp).wrapContentHeight(),
                    imageVector = ArrowRight,
                    contentDescription = null,
                    tint = White,
                )
            }
        }
    }
}