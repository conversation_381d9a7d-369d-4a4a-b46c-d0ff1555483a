package com.forthosewho.app.onboarding.ui.model

import androidx.compose.runtime.Composable
import forthosewho_app.composeapp.generated.resources.Res
import forthosewho_app.composeapp.generated.resources.clues
import forthosewho_app.composeapp.generated.resources.content
import forthosewho_app.composeapp.generated.resources.dialogue
import forthosewho_app.composeapp.generated.resources.logoImage
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_subtitle1
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_subtitle2
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_subtitle3
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_subtitle4
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_title1
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_title2
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_title3
import forthosewho_app.composeapp.generated.resources.onboarding_first_exp_title4
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource

object OnboardingFirstExperienceState {

    data class State(
        val screenState: ScreenState,
        val pagesSize: Int = onboardingPages.size,
        val pages: List<PageContent> = onboardingPages
    ) {
        companion object {
            fun createInitialState(): State = State(
                screenState = ScreenState.StateOnboarding,
            )
        }
    }

    sealed class ScreenState {
        data object StateOnboarding : ScreenState()
        data object StateOnboardingEnd : ScreenState()
    }

    data class PageContent(
        val imageRes: DrawableResource,
        val title: StringResource,
        val subtitle: StringResource
    )

    val onboardingPages = listOf(
        PageContent(
            imageRes = Res.drawable.logoImage,
            title = Res.string.onboarding_first_exp_title1,
            subtitle = Res.string.onboarding_first_exp_subtitle1
        ),
        PageContent(
            imageRes = Res.drawable.dialogue,
            title = Res.string.onboarding_first_exp_title2,
            subtitle = Res.string.onboarding_first_exp_subtitle2
        ),
        PageContent(
            imageRes = Res.drawable.content,
            title = Res.string.onboarding_first_exp_title3,
            subtitle = Res.string.onboarding_first_exp_subtitle3
        ),
        PageContent(
            imageRes = Res.drawable.clues,
            title = Res.string.onboarding_first_exp_title4,
            subtitle = Res.string.onboarding_first_exp_subtitle4
        )
    )

    @Composable
    fun getPageContent(currentPage: Int): PageContent {
        return onboardingPages.getOrNull(currentPage) ?: onboardingPages.first()
    }
}

