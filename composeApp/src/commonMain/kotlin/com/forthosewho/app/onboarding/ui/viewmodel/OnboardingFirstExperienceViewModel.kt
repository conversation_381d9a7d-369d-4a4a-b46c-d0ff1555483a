package com.forthosewho.app.onboarding.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase
import com.forthosewho.app.datastore.domain.usecase.SetStoredValueUseCase.Params
import com.forthosewho.app.onboarding.analytics.OnboardingFirstExperienceEvents
import com.forthosewho.app.onboarding.ui.model.OnboardingFirstExperienceState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class OnboardingFirstExperienceViewModel(
    private val setStoredValueUseCase: SetStoredValueUseCase = SetStoredValueUseCase(),
    private val analyticsUseCase: AnalyticsUseCase
) : ViewModel() {

    private val _state = MutableStateFlow(OnboardingFirstExperienceState.State.createInitialState())
    val state = _state.asStateFlow()

    fun onStateInitial() {
        viewModelScope.launch {
            analyticsUseCase.trackEvent(OnboardingFirstExperienceEvents.SignupCompleted())
            setStoredValueUseCase.apply { invoke(Params(dataStoreValue = DataStoreValues.FIRST_ONBOARDING, value = true)) }
        }
    }

    fun onStateOnboardingEnded() {
        _state.update { state ->
            state.copy(screenState = OnboardingFirstExperienceState.ScreenState.StateOnboardingEnd)
        }
    }
}
