package com.forthosewho.app.platform.utils

private const val EMPTY_STRING = ""

fun String.Companion.empty(): String = EMPTY_STRING

val String.validatedBaseUrl
    get() = if (this.endsWith('/')) this.removeSuffix("/") else this

// Extension function to clean and tokenize the string
fun String.tokeniseClues(): List<String> {
    // Handle empty or null strings
    if (this.isBlank()) return emptyList()

    // Remove outer quotes if present (for JSON string representation)
    val trimmedInput = this.trim().let {
        if (it.startsWith("\"") && it.endsWith("\"")) {
            it.substring(1, it.length - 1)
        } else {
            it
        }
    }

    // Check if it's a JSON array format
    if (trimmedInput.startsWith("[") && trimmedInput.endsWith("]")) {
        // Remove the outer brackets
        val arrayContent = trimmedInput.substring(1, trimmedInput.length - 1)

        // Split by commas that are not inside quotes
        val result = mutableListOf<String>()
        var currentItem = StringBuilder()
        var insideQuotes = false
        var escapeNext = false

        for (char in arrayContent) {
            when {
                escapeNext -> {
                    currentItem.append(char)
                    escapeNext = false
                }
                char == '\\' -> {
                    escapeNext = true
                }
                char == '"' -> {
                    insideQuotes = !insideQuotes
                    // Don't include the quotes in the result
                }
                char == ',' && !insideQuotes -> {
                    // End of an item
                    val item = currentItem.toString().trim()
                    if (item.isNotEmpty()) {
                        // Remove surrounding quotes if present
                        val cleanItem = if (item.startsWith("\"") && item.endsWith("\"")) {
                            item.substring(1, item.length - 1)
                        } else {
                            item
                        }
                        result.add(cleanItem)
                    }
                    currentItem = StringBuilder()
                }
                else -> {
                    currentItem.append(char)
                }
            }
        }

        // Add the last item
        val lastItem = currentItem.toString().trim()
        if (lastItem.isNotEmpty()) {
            // Remove surrounding quotes if present
            val cleanItem = if (lastItem.startsWith("\"") && lastItem.endsWith("\"")) {
                lastItem.substring(1, lastItem.length - 1)
            } else {
                lastItem
            }
            result.add(cleanItem)
        }

        return result
    }

    // Fallback to the original implementation for non-JSON formats
    val cleanedInput = this.replace("\\", "")
        .replace("\"", "")
        .replace("[", "")
        .replace("]", "")
        .replace(",", "")

    return cleanedInput.split("\"")
        .map { it.trim() }
        .filter { it.isNotEmpty() }
}