package com.forthosewho.app.platform.di

import com.forthosewho.app.account.di.accountModule
import com.forthosewho.app.analytics.di.analyticsModule
import com.forthosewho.app.analytics.di.platformModule
import com.forthosewho.app.assistant.di.assistantModule
import com.forthosewho.app.batcave.di.batcaveModule
import com.forthosewho.app.login.di.loginModule
import com.forthosewho.app.network.di.networkingModule
import com.forthosewho.app.onboarding.di.onboardingModule
import com.forthosewho.app.profile_home.di.profileModule
import com.forthosewho.app.stories_home.di.storiesModule
import org.koin.core.context.startKoin
import org.koin.dsl.KoinAppDeclaration

object AppInitializer {
    fun initKoinApp(config: KoinAppDeclaration? = null) {
        startKoin {
            config?.invoke(this)
            modules(
                loginModule,
                onboardingModule,
                networkingModule,
                assistantModule,
                batcaveModule,
                profileModule,
                storiesModule,
                accountModule,
                platformModule(),
                analyticsModule,

            )
        }
    }
}