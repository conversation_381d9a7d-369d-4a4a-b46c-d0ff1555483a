<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">production.forthosewho.com</domain>
        <domain includeSubdomains="true">auth.forthosewho.com</domain>
        <domain includeSubdomains="true">localhost</domain>
    </domain-config>
    <debug-overrides>
        <trust-anchors>
            <!-- Trust user added CAs while debuggable only -->
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>
</network-security-config>
