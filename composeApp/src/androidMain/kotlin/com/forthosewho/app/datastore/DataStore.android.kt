package com.forthosewho.app.datastore

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import com.forthosewho.app.platform.utils.ContextUtils
import kotlinx.coroutines.runBlocking

actual fun createDataStore(): DataStore<Preferences> {
    return runBlocking {
        getDataStore(producePath = { ContextUtils.dataStoreApplicationContext!!.filesDir.resolve(dataStoreFileName).absolutePath })
    }
}
