package com.forthosewho.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.forthosewho.app.platform.di.AppInitializer
import com.forthosewho.app.platform.di.dataStoreModule
import com.forthosewho.app.platform.utils.ContextUtils
import com.forthosewho.app.theme.ForThoseWhoTheme
import com.google.firebase.Firebase
import com.google.firebase.initialize
import io.github.aakira.napier.DebugAntilog
import io.github.aakira.napier.Napier
import org.koin.android.ext.koin.androidContext

class AppActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Firebase.initialize(this)
        ContextUtils.setContext(context = this)
        AppInitializer.initKoinApp {
            androidContext(this@AppActivity)
            dataStoreModule
        }
        Napier.base(DebugAntilog())
        installSplashScreen()
        enableEdgeToEdge()
        setContent {
            ForThoseWhoTheme {
                ForThoseWhoNavHost()
            }
        }
    }
}

@Preview
@Composable
fun AppPreview() {
    ForThoseWhoNavHost()
}