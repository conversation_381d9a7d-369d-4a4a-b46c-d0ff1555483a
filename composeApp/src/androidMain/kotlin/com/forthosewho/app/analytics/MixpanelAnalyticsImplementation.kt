package com.forthosewho.app.analytics

import android.content.Context
import com.mixpanel.android.mpmetrics.MixpanelAPI
import io.github.aakira.napier.Napier
import org.json.JSONObject

class MixpanelAnalyticsImplementation(val applicationContext: Context) : Analytics {

    private var mixpanel: MixpanelAPI? = null

    override fun initialize(token: String) {
        try {
            mixpanel = MixpanelAPI.getInstance(applicationContext, token, true)
            Napier.d("Mixpanel initialized successfully")
        } catch (e: Exception) {
            Napier.e("Failed to initialize Mixpanel", e)
        }
    }

    override fun identify(userId: String) {
        try {
            mixpanel?.identify(userId)
            // In Mixpanel 8.0.2, we use J<PERSON>NObject for setting properties
            val props = JSONObject()
            props.put("distinct_id", userId)
            mixpanel?.people?.set(props)

            Napier.d("Identified user: $userId")
        } catch (e: Exception) {
            Napier.e("Failed to identify user", e)
        }
    }

    override fun timeEvent(eventName: String) {

    }

    override fun track(event: AnalyticsEvent) {
        try {
            // Create a JSONObject (from org.json package) for Mixpanel
            val jsonProperties = JSONObject()

            // Add each property from our map to the JSONObject
            event.properties?.forEach { (key, value) ->
                jsonProperties.put(key, value)
            }

            // Track with the proper JSONObject
            mixpanel?.track(event.eventName, jsonProperties)
            Napier.d("Tracked event: ${event.eventName} with properties: ${event.properties}")
        } catch (e: Exception) {
            Napier.e("Failed to track event: ${event.eventName}", e)
        }
    }

    override fun reset() {
        try {
            mixpanel?.reset()
            Napier.d("Analytics reset")
        } catch (e: Exception) {
            Napier.e("Failed to reset analytics", e)
        }
    }

//    override fun setUserProperty(property: String, value: Any) {
//        try {
//            // Create a JSONObject (from org.json package) for Mixpanel
//            val jsonObject = JSONObject()
//            jsonObject.put(property, value)
//
//            // Set the property using the proper JSONObject
//            mixpanel?.people?.set(jsonObject)
//            Napier.d("Set user property: $property = $value")
//        } catch (e: Exception) {
//            Napier.e("Failed to set user property: $property", e)
//        }
//    }
}