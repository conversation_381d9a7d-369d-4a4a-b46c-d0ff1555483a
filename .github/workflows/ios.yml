name: iOS CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    name: Build and Test
    runs-on: macos-latest

    steps:
      # Checkout repository
      - name: Checkout Repository
        uses: actions/checkout@v4

      # Setup Java
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      # Setup Xcode
      - name: Setup Xcode
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: '15.1'

      # Cache Gradle
      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      # Cache Swift packages
      - name: Cache Swift packages
        uses: actions/cache@v3
        with:
          path: |
            ~/Library/Developer/Xcode/DerivedData/**/SourcePackages/checkouts
            iosApp/.build
          key: ${{ runner.os }}-spm-${{ hashFiles('**/Package.resolved') }}
          restore-keys: |
            ${{ runner.os }}-spm-

      # Build Kotlin framework
      - name: Build ComposeApp Framework
        run: |
          ./gradlew :composeApp:linkDebugFrameworkIosSimulatorArm64
          ./gradlew :composeApp:linkDebugFrameworkIosX64

      # Clean Swift package manager state
      - name: Clean Swift Package Manager State
        run: |
          cd iosApp
          rm -f iosApp.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved
          rm -rf ~/Library/Developer/Xcode/DerivedData/*
          rm -rf .build

      # Resolve Swift packages
      - name: Resolve Swift Packages
        run: |
          cd iosApp
          xcodebuild \
            -project iosApp.xcodeproj \
            -scheme iosApp \
            -resolvePackageDependencies

      # Build iOS project
      - name: Build iOS
        run: |
          cd iosApp
          xcodebuild \
            -project iosApp.xcodeproj \
            -scheme iosApp \
            -configuration Debug \
            -sdk iphonesimulator \
            -destination 'platform=iOS Simulator,name=iPhone 15,OS=17.0' \
            KOTLIN_FRAMEWORK_PATH="$GITHUB_WORKSPACE/composeApp/build/bin/iosSimulatorArm64/debugFramework/" \
            clean build | xcpretty && exit ${PIPESTATUS[0]}

      # Optional: Run Kotlin tests for iOS targets
      - name: Run Kotlin iOS Tests
        run: |
          ./gradlew :composeApp:iosX64Test
          ./gradlew :composeApp:iosSimulatorArm64Test

      # Upload build artifacts
      - name: Upload Build Artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ios-build
          path: |
            composeApp/build/bin/
            iosApp/build/
            iosApp/DerivedData/

      # Upload test results
      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: |
            composeApp/build/reports/tests/
            iosApp/build/reports/
            iosApp/DerivedData/Logs/Test/

      # Report build status
      - name: Report Status
        if: always()
        run: |
          if [ ${{ job.status }} == 'success' ]; then
            echo "✅ Build succeeded"
          else
            echo "❌ Build failed"
          fi

  # Optional: Add notifications job
  notify:
    needs: build
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Check Build Status
        run: |
          if [ "${{ needs.build.result }}" == "success" ]; then
            echo "✅ iOS build and tests passed"
            exit 0
          else
            echo "❌ iOS build or tests failed"
            exit 1
          fi
