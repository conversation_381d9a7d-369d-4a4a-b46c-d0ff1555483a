name: Android CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build-and-test:
    name: Build, Test and Run Maestro Flows
    runs-on: ubuntu-latest
    strategy:
      matrix:
        api-level: [34]
        target: [google_apis]

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      # Build Android app
      - name: Build Debug APK
        run: ./gradlew :composeApp:assembleDebug

      # Run unit tests
      - name: Run Unit Tests
        run: ./gradlew :composeApp:testDebugUnitTest

      # Run Android Lint
      - name: Run Android Lint
        run: ./gradlew :composeApp:lintDebug

      # Enable KVM for better emulator performance
      - name: Enable KVM
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm

      # Install Maestro
      - name: Install Maestro
        run: |
          curl -Ls "https://get.maestro.mobile.dev" | bash
          echo "${HOME}/.maestro/bin" >> $GITHUB_PATH
     
      # Run Tests on Emulator
      - name: Run Tests
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: ${{ matrix.api-level }}
          target: ${{ matrix.target }}
          arch: x86_64
          profile: pixel_3a
          avd-name: pixel_3a_api_34_extension_level_7_x86_64
          script: |
            ./gradlew connectedCheck
            adb install composeApp/build/outputs/apk/debug/composeApp-debug.apk
            maestro test .maestro/android-flow.yaml

      # Upload APK
      - name: Upload Debug APK
        uses: actions/upload-artifact@v4
        with:
          name: app-debug
          path: composeApp/build/outputs/apk/debug/composeApp-debug.apk

      # Upload test results
      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: |
            composeApp/build/reports/tests/testDebugUnitTest/
            .maestro/logs/

      # Upload lint results
      - name: Upload Lint Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: lint-results
          path: composeApp/build/reports/lint-results-debug.html
