{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "command": "./gradlew composeApp:run",
            "name": "Run Desktop App",
            "request": "launch",
            "type": "node-terminal"
        },
        {
            "command": "./android_runner.sh",
            "name": "Run Android App",
            "request": "launch",
            "type": "node-terminal"
        },
        {
            "command": "./gradlew :composeApp:compileIosMainKotlinMetadata",
            "name": "Compile iOS Shared Kotlin Metadata",
            "request": "launch",
            "type": "node-terminal"
        },
    ]
