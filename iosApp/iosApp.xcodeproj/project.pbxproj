// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A93A953B29CC810C00F8E227 /* iosApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A93A953A29CC810C00F8E227 /* iosApp.swift */; };
		A93A953F29CC810D00F8E227 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A93A953E29CC810D00F8E227 /* Assets.xcassets */; };
		A93A954229CC810D00F8E227 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A93A954129CC810D00F8E227 /* Preview Assets.xcassets */; };
		B2240A6F2D9F33CB005D00FB /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = B2240A6E2D9F33CB005D00FB /* FirebaseCrashlytics */; };
		B2240A732DA6A6BD005D00FB /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = B2240A722DA6A6BD005D00FB /* FirebaseAnalytics */; };
		B2240A752DA6A859005D00FB /* SwiftLibDependencyFactoryImpl.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2240A742DA6A858005D00FB /* SwiftLibDependencyFactoryImpl.swift */; };
		B2A13B862D9D980B000CF0D9 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = B2A13B852D9D980B000CF0D9 /* GoogleService-Info.plist */; };
		B2A13B892D9DB47D000CF0D9 /* Mixpanel in Frameworks */ = {isa = PBXBuildFile; productRef = B2A13B882D9DB47D000CF0D9 /* Mixpanel */; };
		B2B64C3B2DA87A540055C09C /* MixpanelAnalyticsImplementation.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2B64C3A2DA87A540055C09C /* MixpanelAnalyticsImplementation.swift */; };
		B2D45ACD2D9DC286006F8D2E /* FirebaseCore in Frameworks */ = {isa = PBXBuildFile; productRef = B2D45ACC2D9DC286006F8D2E /* FirebaseCore */; };
		B2D5E3532D78666400BDA762 /* Mixpanel in Frameworks */ = {isa = PBXBuildFile; productRef = B2D5E3522D78666400BDA762 /* Mixpanel */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A93A953729CC810C00F8E227 /* ForThoseWho App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "ForThoseWho App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A93A953A29CC810C00F8E227 /* iosApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = iosApp.swift; sourceTree = "<group>"; };
		A93A953E29CC810D00F8E227 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A93A954129CC810D00F8E227 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		B2240A742DA6A858005D00FB /* SwiftLibDependencyFactoryImpl.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftLibDependencyFactoryImpl.swift; sourceTree = "<group>"; };
		B2A13B852D9D980B000CF0D9 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		B2B64C3A2DA87A540055C09C /* MixpanelAnalyticsImplementation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MixpanelAnalyticsImplementation.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A93A953429CC810C00F8E227 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B2A13B892D9DB47D000CF0D9 /* Mixpanel in Frameworks */,
				B2240A6F2D9F33CB005D00FB /* FirebaseCrashlytics in Frameworks */,
				B2D45ACD2D9DC286006F8D2E /* FirebaseCore in Frameworks */,
				B2240A732DA6A6BD005D00FB /* FirebaseAnalytics in Frameworks */,
				B2D5E3532D78666400BDA762 /* Mixpanel in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A93A952E29CC810C00F8E227 = {
			isa = PBXGroup;
			children = (
				A93A953929CC810C00F8E227 /* iosApp */,
				A93A953829CC810C00F8E227 /* Products */,
				C4127409AE3703430489E7BC /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A93A953829CC810C00F8E227 /* Products */ = {
			isa = PBXGroup;
			children = (
				A93A953729CC810C00F8E227 /* ForThoseWho App.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A93A953929CC810C00F8E227 /* iosApp */ = {
			isa = PBXGroup;
			children = (
				B2B64C3A2DA87A540055C09C /* MixpanelAnalyticsImplementation.swift */,
				B2240A742DA6A858005D00FB /* SwiftLibDependencyFactoryImpl.swift */,
				A93A953A29CC810C00F8E227 /* iosApp.swift */,
				A93A953E29CC810D00F8E227 /* Assets.xcassets */,
				A93A954029CC810D00F8E227 /* Preview Content */,
				B2A13B852D9D980B000CF0D9 /* GoogleService-Info.plist */,
			);
			path = iosApp;
			sourceTree = "<group>";
		};
		A93A954029CC810D00F8E227 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A93A954129CC810D00F8E227 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		C4127409AE3703430489E7BC /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A93A953629CC810C00F8E227 /* iosApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A93A954529CC810D00F8E227 /* Build configuration list for PBXNativeTarget "iosApp" */;
			buildPhases = (
				A9D80A052AAB5CDE006C8738 /* ShellScript */,
				A93A953329CC810C00F8E227 /* Sources */,
				A93A953429CC810C00F8E227 /* Frameworks */,
				A93A953529CC810C00F8E227 /* Resources */,
				B2240A6D2D9F32FE005D00FB /* Run Script */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = iosApp;
			productName = iosApp;
			productReference = A93A953729CC810C00F8E227 /* ForThoseWho App.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A93A952F29CC810C00F8E227 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1420;
				LastUpgradeCheck = 1420;
				TargetAttributes = {
					A93A953629CC810C00F8E227 = {
						CreatedOnToolsVersion = 14.2;
					};
				};
			};
			buildConfigurationList = A93A953229CC810C00F8E227 /* Build configuration list for PBXProject "iosApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A93A952E29CC810C00F8E227;
			packageReferences = (
				B2D5E3512D78666400BDA762 /* XCRemoteSwiftPackageReference "mixpanel-swift" */,
				B2A13B872D9D9956000CF0D9 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
			);
			productRefGroup = A93A953829CC810C00F8E227 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A93A953629CC810C00F8E227 /* iosApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A93A953529CC810C00F8E227 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A93A954229CC810D00F8E227 /* Preview Assets.xcassets in Resources */,
				A93A953F29CC810D00F8E227 /* Assets.xcassets in Resources */,
				B2A13B862D9D980B000CF0D9 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A9D80A052AAB5CDE006C8738 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "cd \"$SRCROOT/..\"\n./gradlew :composeApp:embedAndSignAppleFrameworkForXcode\n";
		};
		B2240A6D2D9F32FE005D00FB /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist",
				"$(TARGET_BUILD_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist",
				"$(TARGET_BUILD_DIR)/$(EXECUTABLE_PATH)",
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A93A953329CC810C00F8E227 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B2240A752DA6A859005D00FB /* SwiftLibDependencyFactoryImpl.swift in Sources */,
				A93A953B29CC810C00F8E227 /* iosApp.swift in Sources */,
				B2B64C3B2DA87A540055C09C /* MixpanelAnalyticsImplementation.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A93A954329CC810D00F8E227 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A93A954429CC810D00F8E227 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A93A954629CC810D00F8E227 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_ASSET_PATHS = "\"iosApp/Preview Content\"";
				DEVELOPMENT_TEAM = 4LSR39C7PK;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ForThoseWho;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.news";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.1.0;
				OTHER_LDFLAGS = "-lsqlite3";
				PRODUCT_BUNDLE_IDENTIFIER = com.forthosewho.app.ios;
				PRODUCT_NAME = "ForThoseWho App";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A93A954729CC810D00F8E227 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_ASSET_PATHS = "\"iosApp/Preview Content\"";
				DEVELOPMENT_TEAM = 4LSR39C7PK;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ForThoseWho;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.news";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.1.0;
				OTHER_LDFLAGS = "-lsqlite3";
				PRODUCT_BUNDLE_IDENTIFIER = com.forthosewho.app.ios;
				PRODUCT_NAME = "ForThoseWho App";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A93A953229CC810C00F8E227 /* Build configuration list for PBXProject "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A93A954329CC810D00F8E227 /* Debug */,
				A93A954429CC810D00F8E227 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A93A954529CC810D00F8E227 /* Build configuration list for PBXNativeTarget "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A93A954629CC810D00F8E227 /* Debug */,
				A93A954729CC810D00F8E227 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B2A13B872D9D9956000CF0D9 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.11.0;
			};
		};
		B2D5E3512D78666400BDA762 /* XCRemoteSwiftPackageReference "mixpanel-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/mixpanel/mixpanel-swift";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		B2240A6E2D9F33CB005D00FB /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = B2A13B872D9D9956000CF0D9 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
		B2240A722DA6A6BD005D00FB /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = B2A13B872D9D9956000CF0D9 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		B2A13B882D9DB47D000CF0D9 /* Mixpanel */ = {
			isa = XCSwiftPackageProductDependency;
			package = B2D5E3512D78666400BDA762 /* XCRemoteSwiftPackageReference "mixpanel-swift" */;
			productName = Mixpanel;
		};
		B2D45ACC2D9DC286006F8D2E /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = B2A13B872D9D9956000CF0D9 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
		B2D5E3522D78666400BDA762 /* Mixpanel */ = {
			isa = XCSwiftPackageProductDependency;
			package = B2D5E3512D78666400BDA762 /* XCRemoteSwiftPackageReference "mixpanel-swift" */;
			productName = Mixpanel;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A93A952F29CC810C00F8E227 /* Project object */;
}
