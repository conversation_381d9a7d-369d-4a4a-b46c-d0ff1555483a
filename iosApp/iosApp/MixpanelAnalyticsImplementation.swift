import Foundation
import ComposeApp
import Mixpanel

class MixpanelAnalyticsImplementation: ComposeApp.Analytics {

    func initialize(token: String) {
        Mixpanel.initialize(token: token, trackAutomaticEvents: true)
    }

    func track(eventName: AnalyticsEvent) {
        let name = eventName.eventName

        if let properties = eventName.properties {
            // Convert Kotlin Map to Swift Dictionary
            var params = [String: Any]()
            properties.forEach { (key, value) in
                params[key] = value
            }

            Mixpanel.mainInstance().track(event: name, properties: properties)
        } else {
            Mixpanel.mainInstance().track(event: name)
        }
    }
    func identify(userID: String) {
        Mixpanel.mainInstance().identify(distinctId: userID)
    }

    func timeEvent(eventName: String) {
        Mixpanel.mainInstance().time(event: eventName)
    }

    func reset() {
        Mixpanel.mainInstance().reset()
    }
}
