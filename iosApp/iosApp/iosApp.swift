import UIKit
import Compose<PERSON>pp
import <PERSON>base<PERSON>ore

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?

    func application(
        _ application: UIApplication,
       didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        FirebaseApp.configure()
        
        AppInitializer.shared.doInitKoinApp{
            koinApp in koinApp.provideSwiftLibDependencyFactory(factory: SwiftLibDependencyFactoryImpl.shared)
        }

        window = UIWindow(frame: UIScreen.main.bounds)
        if let window = window {
            window.rootViewController = MainKt.MainViewController()
            window.makeKeyAndVisible()
        }
        
        return true
    }
}
